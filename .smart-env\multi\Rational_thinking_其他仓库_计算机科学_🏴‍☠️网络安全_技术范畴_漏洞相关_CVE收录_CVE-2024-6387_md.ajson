"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04405779,-0.02115323,-0.05972689,-0.04206757,0.01638425,-0.02219667,-0.02968057,0.01556328,0.01712221,-0.0040126,-0.0210957,-0.05256023,0.0892257,0.02977741,0.06825516,0.01384607,0.00877864,0.03477594,-0.01382948,0.03944559,0.09506119,-0.01358966,0.03470866,-0.09700976,-0.04381436,0.03657701,0.01421734,-0.0349825,0.00490788,-0.16850048,0.02179485,0.03040412,0.01598814,-0.00210328,0.00913454,0.00856803,0.01597766,0.01819636,-0.03207285,0.04361182,-0.01966387,0.01505328,-0.01301243,-0.03139576,-0.03534928,-0.07677075,-0.01661753,-0.02711713,-0.01258361,-0.05374197,-0.04133097,-0.05160066,-0.06295246,-0.02638654,-0.05260193,-0.03896403,0.02886567,-0.00886465,0.02168561,0.00784681,0.05989976,0.00236041,-0.22943909,0.04116236,0.03425028,-0.01239398,0.01634463,-0.062007,0.00883335,0.02970969,-0.05973609,0.01405803,-0.02340218,0.0180856,0.02547583,0.02393185,-0.01721747,-0.01422584,-0.05191297,-0.05695271,-0.01663891,0.04815239,-0.03397432,0.02102485,0.01188567,0.00220264,-0.00757095,0.02602888,0.07740499,0.01892582,-0.01040822,-0.07274698,0.03890662,0.03627117,-0.00762023,0.03432336,0.06509852,0.02658374,-0.0930519,0.10066431,-0.07393017,0.02467698,-0.05942538,-0.06579392,0.05365159,-0.02581675,-0.00300235,-0.02105793,0.02923462,-0.01490211,-0.07356158,-0.02444069,0.10807393,-0.05612591,0.02657173,0.01085955,0.0177424,-0.01959067,-0.0263056,-0.02274494,0.00980277,0.01369666,0.07363477,-0.02632082,-0.01678085,-0.01413441,0.01325586,0.09085295,0.02447452,0.04285397,0.07477236,-0.03557223,-0.03128826,-0.04503268,-0.0016784,0.04435762,-0.03178124,0.02056311,0.00169457,-0.04413915,-0.02541509,-0.03416495,0.00681117,-0.06004651,-0.08619447,0.04966263,-0.07259252,0.02352473,0.02619307,-0.03504951,0.02441314,0.05238895,-0.03347154,-0.03456656,-0.00645194,-0.02673405,0.04521731,0.12402523,-0.06794392,0.00846552,-0.00380959,-0.00898556,-0.09041868,0.15853645,-0.00318454,-0.05155924,-0.01962336,0.01965629,0.02418112,-0.02231631,0.02891148,-0.01591041,0.06617273,-0.02086021,0.09337822,-0.02929375,-0.0188756,-0.02231891,-0.00001276,0.04484088,0.03064304,-0.0331111,-0.09665803,0.04228161,-0.0055457,-0.08969258,-0.06413101,-0.02870164,-0.02050336,-0.06594049,-0.14062127,0.01165066,-0.09301651,0.00028834,0.00607978,-0.04435331,-0.0140866,0.0246952,0.06108858,-0.04233016,0.08166264,0.02279014,-0.03415052,0.01210038,-0.04970695,-0.0397048,0.00967427,-0.04246091,-0.01245092,0.05409211,0.02471768,0.00890544,0.00113717,0.0471976,-0.0417994,0.012691,-0.0092674,0.0299058,0.02598372,0.04984241,0.00723764,0.00368347,-0.06164613,-0.22017816,-0.033975,0.03480545,-0.01191488,0.02312813,-0.01843746,0.04349624,0.00997257,0.08658287,0.0443901,0.07007148,0.07143371,-0.05337081,0.00405399,-0.00016618,-0.00192164,0.04666283,-0.02005652,-0.06295724,0.01313295,-0.01325036,0.06127033,-0.00291008,-0.02957192,0.04426392,-0.01028394,0.12204321,0.00204371,0.01599301,0.01951705,0.07321586,0.0281215,0.03708836,-0.1664746,0.05650595,-0.01068516,-0.00689501,-0.02142279,-0.00259064,0.00357403,0.0210813,0.0278044,-0.02287956,-0.03957529,-0.01015919,-0.02744506,-0.03949868,0.01255507,-0.01153489,0.05686989,0.00743015,0.0332245,0.04198203,0.01391665,0.00309123,-0.04933407,-0.04588525,-0.05182479,0.02482859,0.05892066,0.00535952,0.00014063,-0.02064889,-0.00165764,0.02535241,-0.0221707,0.02011871,-0.00546403,-0.0198082,-0.00835537,-0.02705722,0.15100633,-0.01487238,0.01058395,0.0433362,-0.01864977,-0.01084379,-0.01117581,0.00718261,-0.00087183,0.06833524,-0.0119024,0.03304749,-0.00851429,-0.00537946,0.0134217,0.06784922,-0.00721592,0.11362902,-0.06039472,-0.05148721,-0.03311758,-0.00362986,0.00416047,0.05667141,0.02481721,-0.29511663,0.03837146,-0.04301753,-0.01046056,0.08480954,0.0194587,0.04178817,-0.0324506,-0.07179812,0.01907128,-0.05750605,0.07971741,-0.00033692,-0.01434586,0.00067501,-0.03389199,0.05659532,-0.00835026,0.04101271,-0.04735946,-0.01443744,0.05582111,0.17379719,-0.01382417,0.04498412,0.00102951,-0.01758684,0.06980176,0.02760465,0.04454769,0.01822724,-0.05156305,0.02287764,-0.03042907,0.02346098,0.07417574,-0.02276927,-0.00232092,0.06347945,-0.01301309,-0.03437284,0.01929423,-0.03793201,0.06136145,0.08596863,-0.01163744,-0.00547051,-0.01689202,0.01712941,0.05013081,0.04228485,0.04142504,-0.02625185,0.00877269,0.00515316,0.0881382,0.04016208,-0.02594475,-0.06264259,-0.04425673,0.04648319,0.01489243,0.08068649,0.13418479,0.03482104],"last_embed":{"hash":"a276421bbc164a10bdd131f284e1b4c4026012607b10751debe598333b5cb4db","tokens":490}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.07942292,0.00545561,0.00356227,0.00020625,-0.01631231,0.03051004,0.05555271,-0.05579404,0.02805428,-0.06214828,0.03738056,0.00704296,-0.00706142,-0.03567031,-0.00481352,0.04257689,-0.05462553,0.02111109,0.00819235,-0.01809699,-0.01625639,0.01154274,0.03559065,0.04292275,0.00188526,0.02286477,0.01892543,-0.0265239,0.05193446,0.02943009,0.00181966,-0.02000454,-0.01384939,0.00287203,-0.05860477,-0.01918498,-0.00593105,-0.00251856,0.02098942,0.02054693,-0.02306268,0.03651293,0.00641235,0.0145709,-0.05104407,-0.01533933,-0.01518894,0.04848889,0.00213895,0.01187958,0.01715162,-0.00777535,0.01759422,0.04613788,0.00040078,0.02222649,-0.03174314,-0.07717302,-0.03364934,-0.0168124,0.03489809,0.01103671,0.06987625,0.00794864,-0.0377126,0.01780782,0.00164374,0.04119223,-0.00000365,-0.00766757,-0.05724371,-0.01662491,0.04026375,-0.01581279,0.08170373,0.01313407,0.03284489,0.02765244,-0.03533452,-0.00252523,-0.07026996,0.0148676,-0.00577688,0.05067524,0.01314195,-0.00647797,0.01784516,-0.08154313,-0.0022221,0.00017414,0.02369717,-0.02430907,0.00508781,-0.00444776,0.02136961,-0.06185676,0.03585368,-0.00069248,0.04092193,-0.03784813,-0.01549639,-0.02977872,-0.05831433,-0.00796109,-0.0538618,-0.03752304,-0.04430891,0.03102949,-0.02238512,-0.00076946,-0.01897286,0.04466692,-0.01489107,-0.03160524,-0.08714137,0.04762463,-0.02197946,-0.01059739,-0.00813594,0.07721499,0.04704153,0.01924721,0.03199645,-0.08497749,-0.10058355,0.01907443,0.00460199,-0.03799159,0.01555304,-0.01632228,0.00454443,0.01512349,-0.03468323,-0.03786382,-0.02332798,0.02627063,0.06266078,-0.03746501,0.0695959,-0.02776668,0.02133805,0.0135347,-0.01578244,-0.00079712,-0.01975284,0.07194048,-0.04378287,-0.0738322,0.00454563,0.0379341,0.02159636,-0.0072506,0.02330188,-0.04313041,-0.00930458,0.03881364,0.03391352,0.07285338,-0.00175716,-0.03968287,-0.05977663,0.02941638,-0.00663155,0.00785668,-0.00608579,-0.02255698,-0.0710106,0.00400232,0.00440521,-0.01911072,0.01678404,0.00093666,-0.00600184,0.0532515,0.04202171,-0.02390533,0.06862543,0.00010199,-0.04861271,-0.02067276,0.03523147,-0.03469035,-0.03664991,0.00708847,-0.01872927,0.05044159,0.01089828,0.0066662,0.01620793,0.01415091,0.02180807,0.02945662,-0.00292301,0.0350111,0.00896072,0.03476733,-0.00835762,0.02590241,-0.04219975,0.03248444,-0.05790114,-0.03608817,-0.01711719,-0.05060931,0.02239616,0.00793807,0.03159232,-0.03960998,-0.05401134,0.07665422,-0.02024743,-0.02848782,0.00012004,-0.0009693,-0.0238779,0.01669997,-0.01964047,0.0525483,0.03414917,-0.01362705,0.03197488,-0.01095169,0.02245779,0.00300908,0.00539019,-0.00780844,0.003532,-0.00854215,0.01217772,0.01072836,-0.00230515,0.07269055,-0.06289549,-0.00983017,-0.05082734,0.04928396,0.02662875,0.06345455,0.00559999,0.02017785,0.0411174,0.00189972,-0.01965612,0.00174638,-0.03886672,-0.00769582,-0.01190991,0.04693101,-0.02081505,0.09868264,0.01474661,0.00303275,0.00095242,0.02227166,0.01875588,-0.00010347,-0.04529928,0.00022815,-0.04161068,-0.05656376,-0.03442702,-0.00343179,-0.04877472,0.06340182,-0.09818278,-0.0315218,-0.01983565,-0.01442671,-0.05475249,0.06057478,0.01975212,-0.01619116,0.0506672,-0.01355935,0.04166402,-0.00892892,-0.02255252,-0.01067057,-0.07503113,-0.0065848,0.00673619,-0.0314994,-0.00449611,0.00310675,-0.01895586,0.03792254,0.00907475,0.00688042,0.01013869,-0.00323637,0.0229419,0.00108743,-0.04270253,0.04802365,-0.06155938,-0.02816827,-0.01131357,-0.03859361,-0.02311894,-0.05514497,-0.01106438,0.0144113,-0.01633153,0.00809208,0.00164445,0.01835444,0.00280785,0.03684182,-0.03312331,-0.02923941,-0.00622544,-0.06427845,0.00671023,0.00334601,-0.01664456,0.08236372,0.02412855,0.04089337,-0.0467913,-0.00395027,-0.05828631,-0.00320813,0.03677868,-0.02155042,0.02711584,-0.07184616,0.01577441,-0.00047607,0.02991827,0.0576134,-0.05716402,0.04524388,0.00940883,-0.01309925,0.0130994,0.02675006,-0.01425681,-0.04352077,0.06637191,-0.09297203,0.04975964,-0.02971962,0.01578404,-0.03476097,0.0246697,0.01938116,-0.01557341,0.03629941,-0.0325491,-0.01632251,0.01549678,-0.000339,-0.02228323,0.02996625,0.0473248,-0.03311972,-0.03881096,-0.0224706,0.0166776,-0.01907391,-0.03177387,-0.04415692,-0.01847127,-0.03455037,-0.03100303,0.01183215,0.054811,0.04709999,-0.03111354,-0.03462478,0.00071439,0.00686367,-0.05344201,-0.03661021,0.00920931,-0.00840163,0.00273733,0.00631107,0.03499994,-0.03461669,0.00804145,-0.04809676,-0.04659917,0.01493588,-0.04719432,-0.00972192,0.00220126,-0.03187879,-0.02998814,0.01132771,-0.03836903,0.02337802,-0.02903316,-0.00364462,0.05634353,-0.00620414,0.01992843,0.01839012,-0.0117712,0.00603963,0.01351335,0.00955435,0.04757074,0.01779436,-0.02014241,0.04079244,-0.03799667,0.04563952,-0.03036486,-0.0258921,-0.06779453,0.03076534,0.0682281,0.01310542,-0.02253091,0.02470352,0.00916339,-0.03341807,0.01902546,0.06743486,-0.0153606,-0.03898768,0.01988258,0.01467166,0.00494319,-0.01884327,0.01616736,0.00027705,0.00644461,0.01853354,-0.04484394,-0.03752482,-0.09591136,-0.01188213,0.04804211,-0.05013905,0.05271571,-0.05399597,-0.03309315,0.05082191,-0.0070696,0.01100535,-0.0091982,0.00170304,0.02054421,0.02662206,-0.01618786,-0.02792295,0.0083245,0.04052477,-0.0460734,0.0085093,-0.06943938,-0.02771184,0.0308772,0.02659255,-0.00910595,-0.06138344,0.01528109,0.04184409,0.00027438,-0.00632483,0.05398019,-0.00200046,-0.00060436,0.0421163,0.00179218,0.00784349,0.01251605,-0.06097267,0.02500716,-0.05102396,0.00044008,-0.02038876,0.01059924,0.08677399,-0.02751095,-0.00616076,-0.01244676,0.03404672,0.05038997,0.02813284,-0.02221805,0.05112658,0.02483639,0.07448133,-0.0141882,0.00636764,-0.02294718,-0.00720441,0.0033085,0.04942901,0.03231946,0.02963056,-0.00352997,-0.02717063,0.04930191,0.05402576,-0.06629086,0.06431928,-0.00433802,-0.00410099,0.07227357,0.02680363,0.01277882,0.01590082,-0.0440122,0.01103747,0.04107998,-0.04184832,-0.00840245,-0.02359376,0.09638564,0.12964521,0.0022356,-0.00578264,-0.00673182,0.02865298,0.01555329,-0.01600366,-0.03148694,0.05752682,-0.06490628,0.02782221,0.00835382,0.03003356,-0.00270364,0.02484497,0.00498423,0.04874266,-0.06842531,0.01595625,0.01606571,-0.01160668,0.02593275,-0.06955878,-0.00395807,-0.02308479,-0.01162656,0.0680217,0.00393973,0.03864663,0.03441202,0.05217716,-0.01961765,0.00784773,0.01306462,-0.01548112,0.08996551,-0.00548176,0.01377793,0.05594043,-0.02367494,-0.04759282,-0.01570948,0.01822997,0.07434426,-0.04100674,-0.00114103,0.0380764,0.04409924,0.0180281,-0.00048869,0.02477933,-0.03092872,-0.06484455,-0.05980922,0.02004802,-0.02913309,-0.0024185,-0.0052384,-0.01421072,0.07725082,-0.00256608,-0.03291366,0.00646141,0.05195696,0.00556103,0.0175316,-0.03347253,0.00810271,0.0016292,0.10826895,-0.02501795,0.02148802,0.02286738,-0.01044823,0.07764906,-0.03126859,0.02600467,0.01194969,-0.00905686,-0.01163687,-0.06572034,0.00364084,0.00367608,-0.02487627,-0.00784239,-0.03626203,-0.00553312,0.0194451,0.02326499,-0.06054851,-0.02719754,-0.00232678,-0.10592342,-0.02950193,-0.03021474,0.02109001,0.05442454,-0.02195811,0.00318623,0.00248033,-0.04615711,0.00778104,-0.01368898,-0.06343364,-0.00928581,-0.01106931,-0.0049606,-0.03250695,-0.07541233,-0.0568102,-0.02261365,0.01046334,0.02266952,0.01522445,-0.00156846,0.06502122,-0.01299184,-0.00297801,-0.02749437,-0.02586122,0.05686171,-0.00506323,0.00346361,-0.06375152,-0.00034803,-0.0492245,-0.02010231,0.03281836,0.01773782,0.03065004,0.00713944,0.03989469,-0.03232142,-0.04348763,-0.01046166,0.04127666,-0.01592685,-0.01247874,-0.02702213,0.00457982,-0.0155908,-0.01546806,0.01766776,0.00649398,0.01877048,0.00639022,0.00510572,-0.04180762,-0.01270358,0.01601567,-0.03839231,-0.03589104,-0.00801496,0.02522183,-0.00859649,-0.071123,0.05315496,-0.04677321,-0.05806854,-0.02315399,0.02884134,0.02623033,0.03958346,0.006826,0.01851123,0.03716872,-0.03552309,-0.1017736,-0.00460643,-0.0312193,0.00181485,0.07408457,0.00100607,0.02642955,-0.02393931,-0.03140359,0.00981452,-0.04401924,-0.02329226,-0.0448132,-0.02457922,0.01338934,-0.08174061,0.01467788,-0.07245958,-0.00407513,0.01857855,-0.02594938,-0.03046545,-0.04611891,-0.00256992,-0.0138058,-0.01734174,0.04341282,0.01685734,0.05860705,0.01739746,-0.04438153,0.01678761,0.02676769,0.04893178,0.0406713,-0.08942149,0.01729827,0.03686332,-0.07885146,-0.03948124,-0.0261508,0.0055977,-0.01708882,0.03257222,-0.0477472,-0.08449798,-0.04521716,-0.02169702,0.02101345,0.01716883,-0.0215739,-0.06227003,0.03837281,0.07288851,0.00019612,-0.03730651,0.00402291,0.02191937,0.05728077,0.00955838,-0.02714854,0.02689942,-0.04952779,0.00326279,-0.06187587,0.00355809,0.08846026,0.04540541,-0.03635745,0.03574077,-0.00468554,0.03848052,-0.04159257,0.08054213,0.00080127,-0.03108004,-0.02324604,-0.00405982,6.4e-7,0.02960307,0.02805211,0.01014713,0.02376002,-0.03052482,0.00016063,-0.04033187,0.03336382,0.01537109],"last_embed":{"tokens":1077,"hash":"1eebsb4"}}},"last_read":{"hash":"1eebsb4","at":1751441876426},"class_name":"SmartSource","outlinks":[{"title":"Secure Shell","target":"SSH协议","line":12},{"title":"SSH协议","target":"SSH协议","line":12},{"title":"条件竞争漏洞","target":"条件竞争漏洞","line":19},{"title":"Linux","target":"Linux","line":20},{"title":"CVE-2024-6387 | Ubuntu","target":"https://ubuntu.com/security/CVE-2024-6387","line":21},{"title":"linux","target":"linux","line":25},{"title":"条件竞争漏洞","target":"条件竞争漏洞","line":47},{"title":"Malloc的工作原理","target":"Malloc的工作原理","line":104},{"title":"cve-2024-6387-poc","target":"https://github.com/zgzhang/cve-2024-6387-poc/blob/main/7etsuo-regreSSHion.c","line":111}],"metadata":{"aliases":["RegreSSHion"],"tags":["网络安全/漏洞/CVE"],"CVE编号":"CVE-2024-6387","漏洞类型":["条件竞争漏洞"],"漏洞等级":"高危"},"blocks":{"#---frontmatter---":[1,10],"#简介":[11,29],"#简介#{1}":[12,14],"#简介#{2}":[15,18],"#简介#{3}":[19,19],"#简介#{4}":[20,20],"#简介#{5}":[21,22],"#简介#{6}":[23,24],"#简介#{7}":[25,25],"#简介#{8}":[26,26],"#简介#{9}":[27,28],"#简介#{10}":[29,29],"#影响版本":[30,38],"#影响版本#{1}":[32,38],"#漏洞原理":[39,109],"#漏洞原理#{1}":[41,41],"#漏洞原理#{2}":[42,42],"#漏洞原理#{3}":[43,43],"#漏洞原理#{4}":[44,47],"#漏洞原理#{5}":[48,49],"#漏洞原理#{6}":[50,50],"#漏洞原理#{7}":[51,60],"#漏洞原理#{8}":[52,60],"#漏洞原理#{9}":[61,72],"#漏洞原理#{10}":[63,72],"#漏洞原理#{11}":[73,74],"#漏洞原理#{12}":[75,83],"#漏洞原理#{13}":[76,83],"#漏洞原理#{14}":[84,85],"#漏洞原理#{15}":[86,99],"#漏洞原理#{16}":[87,99],"#漏洞原理#{17}":[100,101],"#漏洞原理#{18}":[102,104],"#漏洞原理#{19}":[105,108],"#漏洞原理#{20}":[109,109],"#相关POC":[110,115],"#相关POC#{1}":[111,115]},"last_import":{"mtime":1723604955151,"size":4048,"at":1748488128975,"hash":"1eebsb4"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md","last_embed":{"hash":"1eebsb4","at":1751441876426}},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#漏洞原理": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03776587,-0.01385768,-0.02222962,-0.06201618,-0.02163051,-0.02797445,0.01191983,0.021722,0.05768284,0.01364976,-0.00632969,-0.06352311,0.05590446,0.06106694,0.04141788,0.02349689,0.03473502,0.01403274,-0.01285355,0.03031564,0.05113569,-0.00736338,-0.00092769,-0.04818056,-0.01150075,0.03092537,0.02855373,-0.07951309,-0.04924021,-0.15093921,0.01301008,0.02257022,0.00063677,0.00398445,0.00903004,-0.02156798,0.04064208,0.03133558,-0.04206228,0.03334085,0.02381527,0.04509906,0.01540217,-0.01645341,-0.02742315,-0.06346571,-0.01278657,-0.04411795,0.04478358,-0.0634715,-0.04566182,-0.03592207,-0.051364,-0.01542421,-0.06784826,-0.01320462,0.0226156,0.01570869,0.05331847,0.00215743,0.06735149,0.02485129,-0.23021135,0.05737125,0.03066607,-0.04065796,0.01159627,-0.03329312,0.0367764,0.02685823,-0.0796205,-0.00048828,-0.01935967,0.01268442,0.03993239,-0.02184323,-0.00132335,-0.02494645,-0.03999845,-0.04424842,-0.02979433,0.07243073,-0.00005387,0.01880083,0.01689928,0.0545784,-0.02923965,-0.0164884,0.04776385,-0.02577274,-0.0142577,-0.0181501,-0.00169733,0.01348972,-0.03637468,0.02538501,0.03275249,0.00573534,-0.1246442,0.11159991,-0.05347744,0.01125809,-0.05240662,-0.0664719,0.04984861,0.00357539,-0.01442896,-0.00381809,-0.04372564,-0.00708327,-0.06879209,-0.0341676,0.07273264,-0.05145046,0.05498688,0.063839,0.01340419,-0.01555689,-0.04442468,-0.00698413,-0.02655746,0.03107042,0.06636508,-0.02944716,-0.0243753,-0.03920743,0.06223701,0.1029783,0.01114191,0.01648169,0.07389016,-0.03845148,-0.01728129,0.00101028,0.0102308,0.03277206,-0.04987819,0.0210775,0.00790813,-0.07479214,-0.01851978,-0.03962774,0.02554079,-0.11346187,-0.0458312,0.04561564,-0.0544135,0.00867504,0.03165118,-0.08346095,0.02993423,0.01129107,-0.00990871,-0.01159054,0.00245554,0.01093108,0.07268962,0.13833581,-0.04914115,0.01101514,0.01654723,-0.01034136,-0.11150468,0.13345446,-0.02178851,-0.06909873,-0.02837297,-0.01058484,0.03306704,-0.05713988,0.01158981,-0.00756928,0.01871748,-0.02535973,0.09732147,-0.02518017,-0.01461635,-0.00882865,0.00722115,0.01943477,0.00737242,-0.04095381,-0.02724276,0.05335754,-0.03644312,-0.12104909,-0.04720206,-0.04206511,0.03741753,-0.05194988,-0.10709887,-0.04010157,-0.0321489,0.04595211,-0.01521004,-0.05226078,-0.00732187,-0.00897432,0.04791006,-0.00681571,0.07280877,0.00119281,-0.06468116,0.02229827,-0.04746662,0.00290121,0.02358452,-0.04995297,0.03477374,0.07478868,0.03849724,0.02861921,0.00751733,0.01499391,-0.02423883,-0.01628149,0.00274933,0.06715038,0.01211396,0.02775661,0.03499915,0.0452918,-0.05035098,-0.21861844,-0.07876281,0.02731409,-0.03171233,0.04051853,-0.02862605,0.03450917,0.0194361,0.05575061,0.00557487,0.13753341,0.108073,-0.05749624,-0.03258035,0.03715434,0.03159727,0.05902633,0.00086683,-0.0446201,-0.00327021,-0.00149978,0.06539278,-0.02876214,0.0024393,0.02396383,-0.04423022,0.11994493,-0.05380397,0.03104712,0.00340419,0.02703774,0.04463936,0.03496449,-0.1153283,0.05030105,-0.01578083,-0.03259569,0.00453695,-0.02873467,0.01189288,-0.04695431,0.03689343,-0.03136397,-0.06666207,-0.00860158,-0.06001758,-0.02960803,-0.02879058,0.02372611,0.02169472,0.03737738,-0.03053807,0.01801506,0.04028959,0.04709699,-0.05521102,-0.02716311,-0.04541031,-0.03859939,0.0664168,-0.03420703,0.01791237,-0.00485383,0.03170674,0.05310776,0.01474151,0.00823145,0.01043729,-0.00774658,0.03585892,-0.03508873,0.1692386,-0.03072291,0.021879,0.0448112,0.00786951,-0.03603661,-0.06180095,0.00214654,-0.00412169,0.08839158,0.03151241,0.01071026,0.00011097,-0.0021192,0.02392501,0.07847363,0.00262761,0.07858282,-0.04308411,-0.04124636,-0.01763092,0.00853461,-0.01012903,0.04915687,-0.01058876,-0.27696073,0.03193951,-0.0718385,-0.03849862,0.00457067,0.03064534,0.04268692,-0.02204155,-0.09666421,0.02951005,-0.04084196,0.06569122,-0.00670316,0.01454825,0.01578033,-0.02668998,0.05384536,-0.04551175,0.0223022,-0.03186236,-0.01462078,0.06896184,0.17730717,0.0152243,0.07324912,0.01728382,-0.01029221,0.06916837,0.06960403,0.02196284,0.00197511,-0.04728856,-0.0117277,-0.02981295,0.05380879,0.01830179,-0.01336524,0.03724486,0.05840385,-0.01980539,-0.01606805,0.05223637,-0.03241731,0.02828627,0.12715997,0.00669282,-0.04971475,-0.04102995,0.02517899,0.02000371,-0.02073499,0.01123565,-0.00263933,-0.01312418,-0.03457046,0.08855399,0.01421059,-0.01246946,-0.02448371,-0.04176522,0.05178934,-0.00626914,0.07026283,0.08547423,0.03063747],"last_embed":{"hash":"9d933fb59cea9dac2b03369b58370e6b284a193ea774d7b27419e224175d116a","tokens":474}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.0440692,0.01150538,-0.00937767,0.00488331,-0.02069527,0.02903568,0.05175702,-0.02805527,0.03569557,-0.09226911,0.03584284,0.02727318,-0.02277647,-0.02207461,0.02261686,0.01198814,-0.04889242,0.03920715,0.00486959,-0.00231988,-0.00284111,-0.00658704,0.03890717,0.034452,-0.00496537,0.01663736,0.01762806,-0.01083643,0.02110528,0.04757059,0.00503414,-0.05284087,0.0055542,-0.02122945,-0.07449891,0.00245789,0.00536606,-0.01810925,0.00974474,0.01789548,-0.023771,-0.01133116,0.00018789,0.03878757,-0.06680698,0.01866819,0.02325696,0.020582,-0.00173264,-0.00867826,0.00697045,-0.04399468,-0.00567511,0.0096701,0.00071679,0.02046721,-0.01545759,-0.07901967,0.0006375,0.00649776,0.03535233,0.01076129,0.08842106,0.03968087,-0.04620529,0.05421471,0.04533736,0.01030254,0.003134,0.01185884,-0.03928058,-0.0031801,0.05264999,0.00233037,0.00329063,0.00446478,0.01317206,0.00935626,-0.04971201,0.00498286,-0.06427,0.01386006,0.00277657,0.04588343,0.02704611,0.0030106,-0.00236683,-0.09523457,0.0080743,0.04307497,0.04578468,-0.03503044,-0.02404411,0.00093316,0.02521842,-0.07981263,0.01276942,0.03328449,-0.00246088,-0.04783926,0.01710694,-0.01229491,-0.06727491,-0.01635304,-0.05479907,-0.02510074,-0.10705579,-0.02172155,-0.04031771,-0.05742504,-0.03743742,0.01699766,-0.00746079,-0.03123309,-0.10137178,0.0563154,-0.02379453,0.00639701,0.02225335,0.05171235,0.05872209,0.04206506,0.00763476,-0.05790874,-0.07850476,0.05402501,0.02833507,-0.04122445,0.02092985,-0.0098654,-0.013578,0.0114954,-0.03068517,0.00761998,0.0190036,0.03269008,0.06561339,-0.03490844,0.04193515,-0.03489713,0.02085229,-0.01287428,-0.06108955,0.00314715,-0.00030961,0.0665322,-0.06044992,-0.04754659,-0.01251479,0.043642,0.0263176,-0.00666526,-0.01226048,-0.05525651,0.0000277,0.03032113,0.03673352,0.03692863,0.0242031,-0.02508998,-0.07082137,0.01020739,0.00243047,0.00085365,-0.01434873,0.00931954,-0.07992446,-0.00100301,-0.00408398,-0.01680862,0.00656797,-0.00028306,-0.01253369,0.03221985,0.04593067,-0.02175432,0.01713165,0.04735501,-0.04333181,-0.03054329,0.04417666,0.01195604,-0.03499907,-0.02224308,0.00064157,0.04217125,0.02807022,0.02084877,0.03944623,0.02160712,0.00243075,0.02753681,-0.02981048,0.01723844,0.05866279,0.0035956,-0.03018403,-0.01049598,-0.04694984,0.04593779,-0.04989211,-0.0452185,-0.0103882,-0.06078865,0.01892477,-0.01170988,0.02049016,-0.0324502,-0.05115092,0.0791534,-0.00342448,-0.03275031,0.00780266,0.00630986,-0.00725526,0.01238635,-0.00641467,0.09528996,0.04090381,0.00915372,0.06331267,-0.02080957,-0.04993256,-0.04162625,0.0619307,-0.01186812,-0.00273507,-0.0170596,0.03856251,0.02333563,0.00789813,0.10264792,-0.0767685,-0.02119101,-0.06994355,0.08300652,0.03808224,0.05459626,0.00978949,0.02773693,-0.00154056,-0.0041638,-0.01612017,0.0156467,-0.02959032,-0.01906515,-0.01849857,0.02055214,-0.01697588,0.06032208,0.00625357,-0.01320435,-0.0218146,0.06547065,-0.01048101,-0.04468611,-0.02423288,0.00278005,-0.03495304,-0.06142252,0.0057563,0.01213778,-0.05412744,0.03116756,0.01134235,-0.04021465,-0.0432706,-0.01463716,-0.03032578,0.06688174,0.03772007,-0.0287655,0.04891339,-0.0214642,0.00296586,-0.00825123,-0.02139985,-0.02272558,-0.11040001,-0.02342471,-0.01284644,-0.01441029,0.02020365,0.00299079,-0.02389121,0.03543083,0.00644219,0.02259889,-0.00452237,-0.0086719,0.02517599,-0.01109872,-0.03962951,0.03764036,-0.05468762,-0.04071382,-0.00449502,-0.04372032,-0.02029215,-0.05037655,-0.02449302,0.02148136,-0.02940987,0.01261127,0.00695212,0.0208769,0.00484472,-0.02298204,-0.01944488,-0.05482738,0.02391441,-0.06210552,0.0275059,-0.01112171,0.01232498,0.07963513,0.03857759,-0.01018338,-0.03279563,-0.02657769,-0.04524925,0.02668517,0.02803542,-0.00464576,0.01746031,-0.07821871,0.03548114,0.02324476,0.02881526,0.05087329,-0.07546167,0.04231869,0.01168923,-0.02273422,0.02702258,0.03071169,-0.04955089,-0.04625882,0.05001589,-0.05318805,0.01114247,-0.03124071,0.03927221,-0.02933007,0.01294236,0.00360376,-0.02199724,0.04092269,-0.00733643,-0.0305856,0.01995984,-0.01157983,0.00388015,-0.02400893,0.02969135,-0.00615859,-0.02712077,-0.04171796,0.01979203,-0.00530914,-0.01591644,-0.0600209,-0.02286275,-0.0038366,-0.01703528,0.03587807,0.06908463,0.07394053,-0.03335087,-0.01879476,-0.00792747,-0.01172408,-0.04485716,-0.04607995,0.02789951,-0.0140816,-0.0187339,0.0081307,-0.01043832,-0.03435314,-0.00943552,-0.03068447,-0.02733299,0.03982014,-0.06892833,0.021219,-0.00559835,-0.02242318,-0.02538364,0.00566173,-0.06414512,0.01911175,-0.05462592,-0.00886269,0.06282562,0.00719445,0.00716906,0.01114509,-0.01589417,0.02232884,0.03011886,-0.0338636,0.01946366,0.02640871,-0.01002766,0.03691991,-0.04975774,0.06384572,-0.04399756,-0.03493961,-0.03916936,0.0351473,0.02199096,-0.00569142,0.00620052,0.03132112,0.00492945,-0.03221768,-0.02124153,0.01257045,0.01260153,-0.03988508,0.00426076,0.02008664,-0.02475818,-0.03243145,0.00411669,0.02675526,0.01481793,-0.03160936,-0.01663482,-0.04952427,-0.08270474,0.00001135,0.02347014,-0.03519988,0.03504532,-0.04894466,-0.02260078,0.03183443,-0.02165829,-0.05032686,-0.00297208,0.00942721,0.04981375,0.05851717,-0.00714885,-0.01753772,0.01715251,0.05752065,-0.01803393,0.00987122,-0.04083709,-0.01692231,0.0409351,0.00771096,-0.02776242,-0.02705308,0.01742902,0.00776731,0.012957,-0.00702366,0.04775764,-0.02164371,-0.01234895,0.05991342,0.01705975,-0.02570215,-0.01007543,-0.04427404,0.00844072,-0.06535794,0.02304242,-0.00956613,0.01332813,0.05846536,-0.01987567,0.01973482,-0.02127572,0.02670096,0.02226889,0.01734572,0.04483218,0.02450593,0.01422485,0.04830356,-0.05084189,0.03913525,-0.00961443,-0.03573034,-0.00546139,0.06914903,0.01384662,0.03074711,-0.0237011,-0.04450462,0.04335239,0.05350327,-0.02454519,0.09224244,-0.04107918,-0.00458018,-0.00619497,0.05781389,0.01765099,0.01267882,-0.07735594,-0.00099328,0.04358091,-0.04771275,0.00172454,0.01007651,0.09579445,0.08889703,-0.02094161,-0.00837612,0.00033347,0.01392675,0.04627929,0.02356814,0.0043773,0.02178062,-0.03877269,0.00633734,0.02197189,-0.00375309,-0.02396969,0.02825085,0.04974364,0.0142147,-0.04909122,0.02498963,-0.02385121,-0.03016437,0.06195428,-0.04595406,-0.00458898,-0.01023718,-0.02022956,0.05434465,0.05559111,0.03823451,-0.00136648,0.00037751,-0.01695163,-0.01715082,0.02717372,-0.00488277,0.05844586,-0.03075684,0.01013969,0.0273274,0.00909032,-0.02166157,0.0024483,0.02357048,0.05571154,-0.06348013,-0.01558948,0.08186245,0.03690881,0.00601437,-0.00517728,0.01746034,-0.03038322,-0.00629937,-0.08215863,0.01088075,0.01472881,0.0398591,-0.02716151,-0.03332836,0.08747204,0.0183249,-0.04550865,0.01169772,0.01456136,-0.01716737,0.05294077,-0.05157813,0.04590391,0.00282411,0.11164432,-0.02466455,0.00089264,0.03321718,0.0004912,0.04652064,-0.01758514,0.02501757,0.00641518,-0.02505116,-0.00563231,-0.06782024,0.0266972,-0.01361102,-0.02376269,0.01005502,-0.01477945,-0.01646077,0.00502555,0.01394251,-0.02213986,-0.04550668,-0.02646819,-0.12711497,-0.03015306,-0.0543998,-0.01003601,0.02602697,0.01500986,0.02554137,-0.01044661,-0.05169265,0.00496556,-0.0190688,-0.01482105,-0.00830463,-0.00038571,-0.01429265,-0.03033099,-0.03038828,-0.03425951,0.00430597,-0.02464826,0.05720653,0.01343188,-0.00448146,0.03191921,-0.03886624,0.01898896,-0.02054682,-0.01982256,0.04594649,0.03162396,-0.02413779,-0.07466033,-0.02214966,0.00360574,-0.00058957,0.03786613,0.03867653,0.03630307,0.00043551,0.03548452,-0.0393141,-0.04744,-0.03738075,0.03485744,-0.01632029,0.00127247,-0.0417296,0.00015548,-0.00171262,0.00419114,0.04077298,0.01270477,0.02189811,0.01679867,0.01041191,-0.028756,-0.01546391,0.02059699,-0.02234112,-0.01202485,0.01888954,0.03985456,-0.00925022,0.0071837,0.03603311,-0.04669245,-0.0271106,-0.02214956,0.00320475,0.046204,0.02313543,0.00391078,0.04131217,0.04800725,-0.02623724,-0.06136627,0.0244927,-0.01193122,-0.02161394,0.07411684,-0.04169709,0.01791495,-0.01150647,0.0089309,-0.00630493,-0.08003338,-0.00474403,-0.05955456,-0.00801882,0.00135923,-0.09276368,0.01096257,-0.01287559,0.00987397,0.02588581,-0.00629038,-0.04806877,-0.03168394,-0.00959697,-0.0207489,-0.01044024,0.04237344,0.01005067,0.05087008,-0.00503525,-0.04995212,0.00847707,0.00580613,0.01167101,0.02831187,-0.08186222,0.01204076,0.03234935,-0.0698887,-0.02248986,-0.03998576,-0.01300348,-0.00532562,0.03616756,-0.03711889,-0.07111325,-0.07469282,-0.01539961,0.01001859,0.02643556,-0.01567275,-0.092679,0.03547231,0.07005036,0.03178889,-0.02902818,-0.00084589,0.02374473,0.0643135,-0.0395129,0.0268919,0.00586433,-0.0171731,-0.02690275,-0.05768383,0.04867624,0.01328239,0.05187622,-0.01683467,0.04275158,-0.02821639,0.02432088,-0.09372129,0.06558873,0.02602143,-0.01413612,-0.03655745,-0.02948376,0.00000123,0.01776228,-0.05962623,-0.01370316,0.00036256,-0.02974342,-0.03362151,-0.0804697,-0.00541655,0.01690462],"last_embed":{"hash":"8wt587","tokens":671}}},"text":null,"length":0,"last_read":{"hash":"8wt587","at":1748397843247},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#漏洞原理","lines":[39,109],"size":1458,"outlinks":[{"title":"条件竞争漏洞","target":"条件竞争漏洞","line":9},{"title":"Malloc的工作原理","target":"Malloc的工作原理","line":66}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#---frontmatter---": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#---frontmatter---","lines":[1,10],"size":101,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#简介": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#简介","lines":[11,29],"size":516,"outlinks":[{"title":"Secure Shell","target":"SSH协议","line":2},{"title":"SSH协议","target":"SSH协议","line":2},{"title":"条件竞争漏洞","target":"条件竞争漏洞","line":9},{"title":"Linux","target":"Linux","line":10},{"title":"CVE-2024-6387 | Ubuntu","target":"https://ubuntu.com/security/CVE-2024-6387","line":11},{"title":"linux","target":"linux","line":15}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#简介#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#简介#{1}","lines":[12,14],"size":104,"outlinks":[{"title":"Secure Shell","target":"SSH协议","line":1},{"title":"SSH协议","target":"SSH协议","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#简介#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#简介#{2}","lines":[15,18],"size":50,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#简介#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#简介#{3}","lines":[19,19],"size":42,"outlinks":[{"title":"条件竞争漏洞","target":"条件竞争漏洞","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#简介#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#简介#{4}","lines":[20,20],"size":61,"outlinks":[{"title":"Linux","target":"Linux","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#简介#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#简介#{5}","lines":[21,22],"size":85,"outlinks":[{"title":"CVE-2024-6387 | Ubuntu","target":"https://ubuntu.com/security/CVE-2024-6387","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#简介#{6}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#简介#{6}","lines":[23,24],"size":30,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#简介#{7}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#简介#{7}","lines":[25,25],"size":46,"outlinks":[{"title":"linux","target":"linux","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#简介#{8}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#简介#{8}","lines":[26,26],"size":29,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#简介#{9}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#简介#{9}","lines":[27,28],"size":52,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#简介#{10}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#简介#{10}","lines":[29,29],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#影响版本": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#影响版本","lines":[30,38],"size":173,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#影响版本#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#影响版本#{1}","lines":[32,38],"size":165,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#漏洞原理#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#漏洞原理#{1}","lines":[41,41],"size":35,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#漏洞原理#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#漏洞原理#{2}","lines":[42,42],"size":49,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#漏洞原理#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#漏洞原理#{3}","lines":[43,43],"size":33,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#漏洞原理#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#漏洞原理#{4}","lines":[44,47],"size":123,"outlinks":[{"title":"条件竞争漏洞","target":"条件竞争漏洞","line":4}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#漏洞原理#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#漏洞原理#{5}","lines":[48,49],"size":25,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#漏洞原理#{6}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#漏洞原理#{6}","lines":[50,50],"size":33,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#漏洞原理#{7}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#漏洞原理#{7}","lines":[51,60],"size":210,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#漏洞原理#{8}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#漏洞原理#{8}","lines":[52,60],"size":143,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#漏洞原理#{9}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#漏洞原理#{9}","lines":[61,72],"size":211,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#漏洞原理#{10}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#漏洞原理#{10}","lines":[63,72],"size":145,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#漏洞原理#{11}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#漏洞原理#{11}","lines":[73,74],"size":72,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#漏洞原理#{12}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#漏洞原理#{12}","lines":[75,83],"size":134,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#漏洞原理#{13}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#漏洞原理#{13}","lines":[76,83],"size":103,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#漏洞原理#{14}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#漏洞原理#{14}","lines":[84,85],"size":63,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#漏洞原理#{15}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#漏洞原理#{15}","lines":[86,99],"size":263,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#漏洞原理#{16}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#漏洞原理#{16}","lines":[87,99],"size":201,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#漏洞原理#{17}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#漏洞原理#{17}","lines":[100,101],"size":58,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#漏洞原理#{18}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#漏洞原理#{18}","lines":[102,104],"size":62,"outlinks":[{"title":"Malloc的工作原理","target":"Malloc的工作原理","line":3}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#漏洞原理#{19}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#漏洞原理#{19}","lines":[105,108],"size":60,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#漏洞原理#{20}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#漏洞原理#{20}","lines":[109,109],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#相关POC": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#相关POC","lines":[110,115],"size":148,"outlinks":[{"title":"cve-2024-6387-poc","target":"https://github.com/zgzhang/cve-2024-6387-poc/blob/main/7etsuo-regreSSHion.c","line":2}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#相关POC#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md#相关POC#{1}","lines":[111,115],"size":140,"outlinks":[{"title":"cve-2024-6387-poc","target":"https://github.com/zgzhang/cve-2024-6387-poc/blob/main/7etsuo-regreSSHion.c","line":1}],"class_name":"SmartBlock"},
"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04405779,-0.02115323,-0.05972689,-0.04206757,0.01638425,-0.02219667,-0.02968057,0.01556328,0.01712221,-0.0040126,-0.0210957,-0.05256023,0.0892257,0.02977741,0.06825516,0.01384607,0.00877864,0.03477594,-0.01382948,0.03944559,0.09506119,-0.01358966,0.03470866,-0.09700976,-0.04381436,0.03657701,0.01421734,-0.0349825,0.00490788,-0.16850048,0.02179485,0.03040412,0.01598814,-0.00210328,0.00913454,0.00856803,0.01597766,0.01819636,-0.03207285,0.04361182,-0.01966387,0.01505328,-0.01301243,-0.03139576,-0.03534928,-0.07677075,-0.01661753,-0.02711713,-0.01258361,-0.05374197,-0.04133097,-0.05160066,-0.06295246,-0.02638654,-0.05260193,-0.03896403,0.02886567,-0.00886465,0.02168561,0.00784681,0.05989976,0.00236041,-0.22943909,0.04116236,0.03425028,-0.01239398,0.01634463,-0.062007,0.00883335,0.02970969,-0.05973609,0.01405803,-0.02340218,0.0180856,0.02547583,0.02393185,-0.01721747,-0.01422584,-0.05191297,-0.05695271,-0.01663891,0.04815239,-0.03397432,0.02102485,0.01188567,0.00220264,-0.00757095,0.02602888,0.07740499,0.01892582,-0.01040822,-0.07274698,0.03890662,0.03627117,-0.00762023,0.03432336,0.06509852,0.02658374,-0.0930519,0.10066431,-0.07393017,0.02467698,-0.05942538,-0.06579392,0.05365159,-0.02581675,-0.00300235,-0.02105793,0.02923462,-0.01490211,-0.07356158,-0.02444069,0.10807393,-0.05612591,0.02657173,0.01085955,0.0177424,-0.01959067,-0.0263056,-0.02274494,0.00980277,0.01369666,0.07363477,-0.02632082,-0.01678085,-0.01413441,0.01325586,0.09085295,0.02447452,0.04285397,0.07477236,-0.03557223,-0.03128826,-0.04503268,-0.0016784,0.04435762,-0.03178124,0.02056311,0.00169457,-0.04413915,-0.02541509,-0.03416495,0.00681117,-0.06004651,-0.08619447,0.04966263,-0.07259252,0.02352473,0.02619307,-0.03504951,0.02441314,0.05238895,-0.03347154,-0.03456656,-0.00645194,-0.02673405,0.04521731,0.12402523,-0.06794392,0.00846552,-0.00380959,-0.00898556,-0.09041868,0.15853645,-0.00318454,-0.05155924,-0.01962336,0.01965629,0.02418112,-0.02231631,0.02891148,-0.01591041,0.06617273,-0.02086021,0.09337822,-0.02929375,-0.0188756,-0.02231891,-0.00001276,0.04484088,0.03064304,-0.0331111,-0.09665803,0.04228161,-0.0055457,-0.08969258,-0.06413101,-0.02870164,-0.02050336,-0.06594049,-0.14062127,0.01165066,-0.09301651,0.00028834,0.00607978,-0.04435331,-0.0140866,0.0246952,0.06108858,-0.04233016,0.08166264,0.02279014,-0.03415052,0.01210038,-0.04970695,-0.0397048,0.00967427,-0.04246091,-0.01245092,0.05409211,0.02471768,0.00890544,0.00113717,0.0471976,-0.0417994,0.012691,-0.0092674,0.0299058,0.02598372,0.04984241,0.00723764,0.00368347,-0.06164613,-0.22017816,-0.033975,0.03480545,-0.01191488,0.02312813,-0.01843746,0.04349624,0.00997257,0.08658287,0.0443901,0.07007148,0.07143371,-0.05337081,0.00405399,-0.00016618,-0.00192164,0.04666283,-0.02005652,-0.06295724,0.01313295,-0.01325036,0.06127033,-0.00291008,-0.02957192,0.04426392,-0.01028394,0.12204321,0.00204371,0.01599301,0.01951705,0.07321586,0.0281215,0.03708836,-0.1664746,0.05650595,-0.01068516,-0.00689501,-0.02142279,-0.00259064,0.00357403,0.0210813,0.0278044,-0.02287956,-0.03957529,-0.01015919,-0.02744506,-0.03949868,0.01255507,-0.01153489,0.05686989,0.00743015,0.0332245,0.04198203,0.01391665,0.00309123,-0.04933407,-0.04588525,-0.05182479,0.02482859,0.05892066,0.00535952,0.00014063,-0.02064889,-0.00165764,0.02535241,-0.0221707,0.02011871,-0.00546403,-0.0198082,-0.00835537,-0.02705722,0.15100633,-0.01487238,0.01058395,0.0433362,-0.01864977,-0.01084379,-0.01117581,0.00718261,-0.00087183,0.06833524,-0.0119024,0.03304749,-0.00851429,-0.00537946,0.0134217,0.06784922,-0.00721592,0.11362902,-0.06039472,-0.05148721,-0.03311758,-0.00362986,0.00416047,0.05667141,0.02481721,-0.29511663,0.03837146,-0.04301753,-0.01046056,0.08480954,0.0194587,0.04178817,-0.0324506,-0.07179812,0.01907128,-0.05750605,0.07971741,-0.00033692,-0.01434586,0.00067501,-0.03389199,0.05659532,-0.00835026,0.04101271,-0.04735946,-0.01443744,0.05582111,0.17379719,-0.01382417,0.04498412,0.00102951,-0.01758684,0.06980176,0.02760465,0.04454769,0.01822724,-0.05156305,0.02287764,-0.03042907,0.02346098,0.07417574,-0.02276927,-0.00232092,0.06347945,-0.01301309,-0.03437284,0.01929423,-0.03793201,0.06136145,0.08596863,-0.01163744,-0.00547051,-0.01689202,0.01712941,0.05013081,0.04228485,0.04142504,-0.02625185,0.00877269,0.00515316,0.0881382,0.04016208,-0.02594475,-0.06264259,-0.04425673,0.04648319,0.01489243,0.08068649,0.13418479,0.03482104],"last_embed":{"hash":"a276421bbc164a10bdd131f284e1b4c4026012607b10751debe598333b5cb4db","tokens":490}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.07942292,0.00545561,0.00356227,0.00020625,-0.01631231,0.03051004,0.05555271,-0.05579404,0.02805428,-0.06214828,0.03738056,0.00704296,-0.00706142,-0.03567031,-0.00481352,0.04257689,-0.05462553,0.02111109,0.00819235,-0.01809699,-0.01625639,0.01154274,0.03559065,0.04292275,0.00188526,0.02286477,0.01892543,-0.0265239,0.05193446,0.02943009,0.00181966,-0.02000454,-0.01384939,0.00287203,-0.05860477,-0.01918498,-0.00593105,-0.00251856,0.02098942,0.02054693,-0.02306268,0.03651293,0.00641235,0.0145709,-0.05104407,-0.01533933,-0.01518894,0.04848889,0.00213895,0.01187958,0.01715162,-0.00777535,0.01759422,0.04613788,0.00040078,0.02222649,-0.03174314,-0.07717302,-0.03364934,-0.0168124,0.03489809,0.01103671,0.06987625,0.00794864,-0.0377126,0.01780782,0.00164374,0.04119223,-0.00000365,-0.00766757,-0.05724371,-0.01662491,0.04026375,-0.01581279,0.08170373,0.01313407,0.03284489,0.02765244,-0.03533452,-0.00252523,-0.07026996,0.0148676,-0.00577688,0.05067524,0.01314195,-0.00647797,0.01784516,-0.08154313,-0.0022221,0.00017414,0.02369717,-0.02430907,0.00508781,-0.00444776,0.02136961,-0.06185676,0.03585368,-0.00069248,0.04092193,-0.03784813,-0.01549639,-0.02977872,-0.05831433,-0.00796109,-0.0538618,-0.03752304,-0.04430891,0.03102949,-0.02238512,-0.00076946,-0.01897286,0.04466692,-0.01489107,-0.03160524,-0.08714137,0.04762463,-0.02197946,-0.01059739,-0.00813594,0.07721499,0.04704153,0.01924721,0.03199645,-0.08497749,-0.10058355,0.01907443,0.00460199,-0.03799159,0.01555304,-0.01632228,0.00454443,0.01512349,-0.03468323,-0.03786382,-0.02332798,0.02627063,0.06266078,-0.03746501,0.0695959,-0.02776668,0.02133805,0.0135347,-0.01578244,-0.00079712,-0.01975284,0.07194048,-0.04378287,-0.0738322,0.00454563,0.0379341,0.02159636,-0.0072506,0.02330188,-0.04313041,-0.00930458,0.03881364,0.03391352,0.07285338,-0.00175716,-0.03968287,-0.05977663,0.02941638,-0.00663155,0.00785668,-0.00608579,-0.02255698,-0.0710106,0.00400232,0.00440521,-0.01911072,0.01678404,0.00093666,-0.00600184,0.0532515,0.04202171,-0.02390533,0.06862543,0.00010199,-0.04861271,-0.02067276,0.03523147,-0.03469035,-0.03664991,0.00708847,-0.01872927,0.05044159,0.01089828,0.0066662,0.01620793,0.01415091,0.02180807,0.02945662,-0.00292301,0.0350111,0.00896072,0.03476733,-0.00835762,0.02590241,-0.04219975,0.03248444,-0.05790114,-0.03608817,-0.01711719,-0.05060931,0.02239616,0.00793807,0.03159232,-0.03960998,-0.05401134,0.07665422,-0.02024743,-0.02848782,0.00012004,-0.0009693,-0.0238779,0.01669997,-0.01964047,0.0525483,0.03414917,-0.01362705,0.03197488,-0.01095169,0.02245779,0.00300908,0.00539019,-0.00780844,0.003532,-0.00854215,0.01217772,0.01072836,-0.00230515,0.07269055,-0.06289549,-0.00983017,-0.05082734,0.04928396,0.02662875,0.06345455,0.00559999,0.02017785,0.0411174,0.00189972,-0.01965612,0.00174638,-0.03886672,-0.00769582,-0.01190991,0.04693101,-0.02081505,0.09868264,0.01474661,0.00303275,0.00095242,0.02227166,0.01875588,-0.00010347,-0.04529928,0.00022815,-0.04161068,-0.05656376,-0.03442702,-0.00343179,-0.04877472,0.06340182,-0.09818278,-0.0315218,-0.01983565,-0.01442671,-0.05475249,0.06057478,0.01975212,-0.01619116,0.0506672,-0.01355935,0.04166402,-0.00892892,-0.02255252,-0.01067057,-0.07503113,-0.0065848,0.00673619,-0.0314994,-0.00449611,0.00310675,-0.01895586,0.03792254,0.00907475,0.00688042,0.01013869,-0.00323637,0.0229419,0.00108743,-0.04270253,0.04802365,-0.06155938,-0.02816827,-0.01131357,-0.03859361,-0.02311894,-0.05514497,-0.01106438,0.0144113,-0.01633153,0.00809208,0.00164445,0.01835444,0.00280785,0.03684182,-0.03312331,-0.02923941,-0.00622544,-0.06427845,0.00671023,0.00334601,-0.01664456,0.08236372,0.02412855,0.04089337,-0.0467913,-0.00395027,-0.05828631,-0.00320813,0.03677868,-0.02155042,0.02711584,-0.07184616,0.01577441,-0.00047607,0.02991827,0.0576134,-0.05716402,0.04524388,0.00940883,-0.01309925,0.0130994,0.02675006,-0.01425681,-0.04352077,0.06637191,-0.09297203,0.04975964,-0.02971962,0.01578404,-0.03476097,0.0246697,0.01938116,-0.01557341,0.03629941,-0.0325491,-0.01632251,0.01549678,-0.000339,-0.02228323,0.02996625,0.0473248,-0.03311972,-0.03881096,-0.0224706,0.0166776,-0.01907391,-0.03177387,-0.04415692,-0.01847127,-0.03455037,-0.03100303,0.01183215,0.054811,0.04709999,-0.03111354,-0.03462478,0.00071439,0.00686367,-0.05344201,-0.03661021,0.00920931,-0.00840163,0.00273733,0.00631107,0.03499994,-0.03461669,0.00804145,-0.04809676,-0.04659917,0.01493588,-0.04719432,-0.00972192,0.00220126,-0.03187879,-0.02998814,0.01132771,-0.03836903,0.02337802,-0.02903316,-0.00364462,0.05634353,-0.00620414,0.01992843,0.01839012,-0.0117712,0.00603963,0.01351335,0.00955435,0.04757074,0.01779436,-0.02014241,0.04079244,-0.03799667,0.04563952,-0.03036486,-0.0258921,-0.06779453,0.03076534,0.0682281,0.01310542,-0.02253091,0.02470352,0.00916339,-0.03341807,0.01902546,0.06743486,-0.0153606,-0.03898768,0.01988258,0.01467166,0.00494319,-0.01884327,0.01616736,0.00027705,0.00644461,0.01853354,-0.04484394,-0.03752482,-0.09591136,-0.01188213,0.04804211,-0.05013905,0.05271571,-0.05399597,-0.03309315,0.05082191,-0.0070696,0.01100535,-0.0091982,0.00170304,0.02054421,0.02662206,-0.01618786,-0.02792295,0.0083245,0.04052477,-0.0460734,0.0085093,-0.06943938,-0.02771184,0.0308772,0.02659255,-0.00910595,-0.06138344,0.01528109,0.04184409,0.00027438,-0.00632483,0.05398019,-0.00200046,-0.00060436,0.0421163,0.00179218,0.00784349,0.01251605,-0.06097267,0.02500716,-0.05102396,0.00044008,-0.02038876,0.01059924,0.08677399,-0.02751095,-0.00616076,-0.01244676,0.03404672,0.05038997,0.02813284,-0.02221805,0.05112658,0.02483639,0.07448133,-0.0141882,0.00636764,-0.02294718,-0.00720441,0.0033085,0.04942901,0.03231946,0.02963056,-0.00352997,-0.02717063,0.04930191,0.05402576,-0.06629086,0.06431928,-0.00433802,-0.00410099,0.07227357,0.02680363,0.01277882,0.01590082,-0.0440122,0.01103747,0.04107998,-0.04184832,-0.00840245,-0.02359376,0.09638564,0.12964521,0.0022356,-0.00578264,-0.00673182,0.02865298,0.01555329,-0.01600366,-0.03148694,0.05752682,-0.06490628,0.02782221,0.00835382,0.03003356,-0.00270364,0.02484497,0.00498423,0.04874266,-0.06842531,0.01595625,0.01606571,-0.01160668,0.02593275,-0.06955878,-0.00395807,-0.02308479,-0.01162656,0.0680217,0.00393973,0.03864663,0.03441202,0.05217716,-0.01961765,0.00784773,0.01306462,-0.01548112,0.08996551,-0.00548176,0.01377793,0.05594043,-0.02367494,-0.04759282,-0.01570948,0.01822997,0.07434426,-0.04100674,-0.00114103,0.0380764,0.04409924,0.0180281,-0.00048869,0.02477933,-0.03092872,-0.06484455,-0.05980922,0.02004802,-0.02913309,-0.0024185,-0.0052384,-0.01421072,0.07725082,-0.00256608,-0.03291366,0.00646141,0.05195696,0.00556103,0.0175316,-0.03347253,0.00810271,0.0016292,0.10826895,-0.02501795,0.02148802,0.02286738,-0.01044823,0.07764906,-0.03126859,0.02600467,0.01194969,-0.00905686,-0.01163687,-0.06572034,0.00364084,0.00367608,-0.02487627,-0.00784239,-0.03626203,-0.00553312,0.0194451,0.02326499,-0.06054851,-0.02719754,-0.00232678,-0.10592342,-0.02950193,-0.03021474,0.02109001,0.05442454,-0.02195811,0.00318623,0.00248033,-0.04615711,0.00778104,-0.01368898,-0.06343364,-0.00928581,-0.01106931,-0.0049606,-0.03250695,-0.07541233,-0.0568102,-0.02261365,0.01046334,0.02266952,0.01522445,-0.00156846,0.06502122,-0.01299184,-0.00297801,-0.02749437,-0.02586122,0.05686171,-0.00506323,0.00346361,-0.06375152,-0.00034803,-0.0492245,-0.02010231,0.03281836,0.01773782,0.03065004,0.00713944,0.03989469,-0.03232142,-0.04348763,-0.01046166,0.04127666,-0.01592685,-0.01247874,-0.02702213,0.00457982,-0.0155908,-0.01546806,0.01766776,0.00649398,0.01877048,0.00639022,0.00510572,-0.04180762,-0.01270358,0.01601567,-0.03839231,-0.03589104,-0.00801496,0.02522183,-0.00859649,-0.071123,0.05315496,-0.04677321,-0.05806854,-0.02315399,0.02884134,0.02623033,0.03958346,0.006826,0.01851123,0.03716872,-0.03552309,-0.1017736,-0.00460643,-0.0312193,0.00181485,0.07408457,0.00100607,0.02642955,-0.02393931,-0.03140359,0.00981452,-0.04401924,-0.02329226,-0.0448132,-0.02457922,0.01338934,-0.08174061,0.01467788,-0.07245958,-0.00407513,0.01857855,-0.02594938,-0.03046545,-0.04611891,-0.00256992,-0.0138058,-0.01734174,0.04341282,0.01685734,0.05860705,0.01739746,-0.04438153,0.01678761,0.02676769,0.04893178,0.0406713,-0.08942149,0.01729827,0.03686332,-0.07885146,-0.03948124,-0.0261508,0.0055977,-0.01708882,0.03257222,-0.0477472,-0.08449798,-0.04521716,-0.02169702,0.02101345,0.01716883,-0.0215739,-0.06227003,0.03837281,0.07288851,0.00019612,-0.03730651,0.00402291,0.02191937,0.05728077,0.00955838,-0.02714854,0.02689942,-0.04952779,0.00326279,-0.06187587,0.00355809,0.08846026,0.04540541,-0.03635745,0.03574077,-0.00468554,0.03848052,-0.04159257,0.08054213,0.00080127,-0.03108004,-0.02324604,-0.00405982,6.4e-7,0.02960307,0.02805211,0.01014713,0.02376002,-0.03052482,0.00016063,-0.04033187,0.03336382,0.01537109],"last_embed":{"tokens":1077,"hash":"1eebsb4"}}},"last_read":{"hash":"1eebsb4","at":1751597369543},"class_name":"SmartSource","outlinks":[{"title":"Secure Shell","target":"SSH协议","line":12},{"title":"SSH协议","target":"SSH协议","line":12},{"title":"条件竞争漏洞","target":"条件竞争漏洞","line":19},{"title":"Linux","target":"Linux","line":20},{"title":"CVE-2024-6387 | Ubuntu","target":"https://ubuntu.com/security/CVE-2024-6387","line":21},{"title":"linux","target":"linux","line":25},{"title":"条件竞争漏洞","target":"条件竞争漏洞","line":47},{"title":"Malloc的工作原理","target":"Malloc的工作原理","line":104},{"title":"cve-2024-6387-poc","target":"https://github.com/zgzhang/cve-2024-6387-poc/blob/main/7etsuo-regreSSHion.c","line":111}],"metadata":{"aliases":["RegreSSHion"],"tags":["网络安全/漏洞/CVE"],"CVE编号":"CVE-2024-6387","漏洞类型":["条件竞争漏洞"],"漏洞等级":"高危"},"blocks":{"#---frontmatter---":[1,10],"#简介":[11,29],"#简介#{1}":[12,14],"#简介#{2}":[15,18],"#简介#{3}":[19,19],"#简介#{4}":[20,20],"#简介#{5}":[21,22],"#简介#{6}":[23,24],"#简介#{7}":[25,25],"#简介#{8}":[26,26],"#简介#{9}":[27,28],"#简介#{10}":[29,29],"#影响版本":[30,38],"#影响版本#{1}":[32,38],"#漏洞原理":[39,109],"#漏洞原理#{1}":[41,41],"#漏洞原理#{2}":[42,42],"#漏洞原理#{3}":[43,43],"#漏洞原理#{4}":[44,47],"#漏洞原理#{5}":[48,49],"#漏洞原理#{6}":[50,50],"#漏洞原理#{7}":[51,60],"#漏洞原理#{8}":[52,60],"#漏洞原理#{9}":[61,72],"#漏洞原理#{10}":[63,72],"#漏洞原理#{11}":[73,74],"#漏洞原理#{12}":[75,83],"#漏洞原理#{13}":[76,83],"#漏洞原理#{14}":[84,85],"#漏洞原理#{15}":[86,99],"#漏洞原理#{16}":[87,99],"#漏洞原理#{17}":[100,101],"#漏洞原理#{18}":[102,104],"#漏洞原理#{19}":[105,108],"#漏洞原理#{20}":[109,109],"#相关POC":[110,115],"#相关POC#{1}":[111,115]},"last_import":{"mtime":1723604955151,"size":4048,"at":1748488128975,"hash":"1eebsb4"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录/CVE-2024-6387.md","last_embed":{"hash":"1eebsb4","at":1751597369543}},