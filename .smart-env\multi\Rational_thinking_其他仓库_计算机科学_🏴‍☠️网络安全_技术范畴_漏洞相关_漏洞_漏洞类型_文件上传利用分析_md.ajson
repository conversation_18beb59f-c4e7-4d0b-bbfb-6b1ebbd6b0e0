"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用分析.md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用分析.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09462222,-0.01923768,-0.01612997,-0.04749021,0.04948711,-0.01019118,-0.04289489,0.00587963,0.03327803,0.00040269,0.04075101,-0.03911361,0.05893571,0.04393715,0.05200091,0.02516581,0.02502154,-0.02558608,-0.03734597,0.00654109,0.08166409,-0.04439339,0.00682757,-0.05333884,0.00467805,-0.0134442,0.00656943,-0.01227648,-0.02542433,-0.15531076,-0.0078654,-0.01973325,0.02759171,0.00970274,-0.01866695,-0.05606939,0.04578136,0.05381802,0.00584874,-0.00575111,-0.01691345,0.0516751,0.00354944,-0.03920261,-0.03977687,-0.02841358,-0.05713505,-0.03215012,0.02434182,-0.04092974,-0.0487265,-0.04857851,-0.04235604,-0.01976423,-0.06045689,-0.02508279,0.00283022,-0.00439284,0.03069128,0.01643036,0.02265421,0.02854977,-0.1990931,0.06424934,0.02254166,-0.03971618,-0.04635021,0.00233898,0.01400838,0.06156438,-0.09865025,0.04994658,-0.01919802,0.0652005,0.06007127,-0.03862051,0.04696644,-0.01831668,-0.06201693,-0.05958141,-0.02871979,0.04752,-0.04099698,0.00790129,-0.00367671,0.02153619,-0.01145183,-0.02494825,-0.04111372,-0.00412662,0.00502263,-0.02625936,-0.02126791,0.03318124,-0.00114821,-0.00289558,0.01482597,0.06191507,-0.04826097,0.11423076,-0.09203974,0.00041009,0.00567626,-0.06804495,-0.01599133,-0.02906409,0.00112934,-0.01704689,0.02971924,-0.00847941,-0.04811572,-0.03360438,0.0508758,-0.02845177,0.05279443,0.03693878,0.00117883,-0.00300957,-0.03879887,-0.04789023,-0.01402239,-0.00223272,0.08103579,-0.02360463,-0.03516716,-0.0263861,0.06407425,0.05823267,0.03678267,0.02977201,0.06293312,0.00273813,-0.06891716,-0.04092304,-0.01216241,-0.00208569,-0.03196581,0.01790089,-0.02909747,-0.03050356,-0.01718522,-0.06854135,-0.00360346,-0.10006005,-0.07995033,0.08737714,-0.05993288,-0.02771408,0.07191657,-0.0513106,-0.00236555,0.09264424,-0.03413571,-0.01481069,-0.0071268,-0.00980522,0.08809084,0.12800789,-0.03662334,-0.01771437,-0.00085336,0.02628954,-0.08406401,0.16950743,0.01132386,-0.07827845,-0.02187753,-0.00524164,0.0375329,-0.02286885,0.03153723,-0.01828952,0.03946576,-0.00326367,0.0708657,-0.0055179,0.00652501,-0.05239879,0.02096706,0.03827751,0.09810815,-0.02046459,-0.07712024,0.07316347,-0.01262238,-0.07859957,-0.06085514,-0.00415076,0.02117497,-0.02269131,-0.1032545,0.05309033,-0.03799171,-0.01959885,-0.05601636,-0.06278994,0.03235475,0.02005653,0.01850383,-0.0725852,0.10052647,0.03094778,-0.01641498,0.01717395,-0.0178366,-0.01373302,0.01289952,-0.00579233,0.02050531,0.00787058,0.01120474,-0.0234531,-0.009392,-0.00518439,-0.02946299,0.05316844,0.0265153,0.00030626,0.02642563,0.08449998,-0.00240875,-0.06495261,-0.03968984,-0.19600046,-0.00666366,0.0034163,-0.04103468,0.00652498,-0.00890556,0.04458727,0.0341438,0.09804133,0.10196066,0.07107262,0.01453052,-0.08934161,-0.02599818,0.0075769,-0.02639434,0.01796004,-0.01342915,-0.02890557,0.01038512,0.00621025,0.04453264,-0.02355314,-0.02106496,0.0802526,-0.0330886,0.12708603,0.05511045,0.0113752,0.04462953,0.05581607,0.05892207,-0.00523364,-0.11053745,0.03734174,0.00869461,-0.03701425,-0.04182768,-0.0265129,-0.02658111,0.03067763,0.00847834,-0.02807629,-0.07202344,-0.01894101,-0.01722175,-0.02378266,0.01634025,0.03535344,0.02358543,0.00060117,-0.00752752,0.00972855,0.0095125,-0.03543029,-0.05395663,-0.03585064,0.00281775,-0.01642126,0.01120958,0.07051551,-0.00779701,0.04984626,0.02247521,-0.01473971,-0.03200826,-0.01717201,-0.02668327,-0.0726604,-0.00144987,0.00784383,0.14605998,-0.01560402,-0.01541393,0.03281747,0.01441455,0.00844849,-0.01070024,0.0065837,-0.01361854,0.02889049,0.0311453,0.04175335,0.02320294,-0.02532883,0.02492027,0.01749372,-0.02082945,0.08324821,-0.04473065,-0.04085613,-0.01150614,-0.05258248,0.02232081,0.0762811,-0.01688825,-0.31262717,0.02159448,-0.01277642,0.01733223,0.05872313,0.02642968,0.06659307,0.04226892,-0.013103,0.03806406,-0.06540496,0.04208437,-0.01726578,-0.04889084,-0.02546877,-0.03649322,0.02935713,0.01813185,0.09145683,-0.00626071,0.03082542,0.05178475,0.2209383,0.00149175,0.0461713,-0.00449667,-0.00312657,0.05110531,0.02337128,0.02512657,0.02029496,-0.04201565,0.04428285,-0.03725993,0.05392777,0.04089808,-0.0447799,0.03591456,0.00759623,0.01871804,-0.04293136,0.04380971,-0.0819955,0.03157792,0.07255892,0.03214758,-0.01894077,-0.02427264,-0.01927041,0.06192324,0.0001219,0.00765832,0.00276689,-0.01406635,0.02061214,0.03420734,0.03333508,-0.03735314,-0.06005366,-0.00908255,0.04969377,0.01268549,0.10078902,0.132881,0.08315642],"last_embed":{"hash":"0c3dd77631514e6dcd620057b55f3620267dcb89edc4aa9e2f8ac53cd08aa707","tokens":418}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.06924506,0.00110352,-0.02270507,-0.0249665,0.01115249,-0.02661869,0.06251435,0.02173182,-0.02865702,-0.03749422,-0.04676188,-0.02698586,-0.08796378,-0.05248044,0.05673917,0.07073528,0.00596425,0.01634131,0.06082423,-0.03384754,-0.02640466,0.0537894,0.05903397,-0.00994853,-0.02128109,-0.00097565,0.00025795,-0.02560222,0.03296933,0.07334772,0.07950655,-0.02560655,0.02574745,0.01481024,-0.05764888,-0.05946704,0.02241926,-0.03781897,-0.00349531,0.01610952,-0.03572265,0.007619,-0.00445859,0.03981923,-0.04844574,-0.01102196,-0.02839859,-0.03193638,-0.00924423,-0.00077965,0.03851888,0.02836069,-0.01450542,0.01675958,0.01100974,0.04608507,-0.0310591,-0.05091761,-0.00072057,-0.01214599,0.02589943,-0.04637446,0.06972103,0.03146876,0.02496338,-0.00633287,-0.01850403,0.0000494,-0.01477005,-0.03288736,-0.05520573,-0.03961279,0.0044985,0.04367771,0.06684505,-0.01501284,0.06730701,0.00057342,0.04661007,0.02795289,-0.06314786,0.02279959,-0.01743706,0.02145857,-0.01419951,0.01282807,0.09860677,-0.07026435,0.04818104,-0.0020036,-0.00763328,0.00000779,-0.0245968,-0.06087473,0.04204957,-0.02073602,0.05674352,-0.01365962,0.01988637,-0.01318354,-0.01223954,0.00150893,-0.06518818,-0.00365342,-0.03361795,-0.09563023,0.00868438,0.0526514,-0.02676688,0.00367405,0.00790611,0.01273345,-0.0311335,0.02944743,0.02424596,0.05236,-0.03755531,0.00574103,0.00633902,0.05161665,0.01226062,0.04212836,0.03270948,-0.03990059,-0.04126802,0.00164382,-0.03877624,-0.02905175,0.03947436,0.02538512,-0.01680165,0.04926629,-0.03546347,-0.02383007,0.05703918,0.01562143,0.02321992,-0.03996455,0.0622747,-0.0478425,0.02020326,-0.02160926,-0.02903677,0.00749199,-0.04169759,0.01182613,-0.02145981,-0.06020745,-0.05669768,0.04890424,0.06070299,0.0223171,-0.04111281,-0.04535325,-0.00624742,-0.0160103,-0.00064713,0.01293562,0.02105995,-0.00803859,-0.00461583,0.04111095,0.00036996,-0.00523682,-0.01584904,-0.00452164,0.01257817,0.04184115,0.01566415,0.02589862,0.00959883,0.02431069,-0.01051132,0.02326974,0.01975343,-0.0602612,0.05899235,0.05059131,-0.0350797,-0.02596804,0.03317826,-0.05436733,-0.01157512,0.05444827,0.01936599,0.02481391,-0.04097705,0.02131812,0.00823432,0.00810703,0.04841996,0.032445,0.01715794,0.0334444,-0.01387007,0.02316258,0.02398351,-0.08632343,-0.02297284,0.03525326,0.02162785,-0.00250841,-0.0278463,-0.08443438,0.01334443,-0.02528349,-0.02329516,-0.0046792,-0.03291419,0.06943197,-0.05109219,-0.00127841,0.01125223,0.01869241,-0.03941694,0.03262444,-0.01442604,-0.00197173,0.0147463,-0.01298351,0.00769374,-0.04462582,-0.07145239,-0.02329507,-0.05539097,-0.02288734,-0.01205948,0.00049156,0.03706796,0.00785159,-0.04335263,0.00604716,-0.0238617,-0.00098843,-0.01826673,-0.01595442,0.06263755,0.01722294,0.01152954,0.00349598,0.01193602,0.04441283,0.02501122,-0.00310662,-0.02815284,-0.03030094,-0.04380113,0.04297959,0.01266471,0.01874606,0.01693622,0.03009495,-0.00927284,0.04429941,-0.00867295,-0.07492802,-0.02036307,-0.00800475,-0.00777409,-0.03598901,0.03370573,-0.01133873,-0.06033148,0.01476286,-0.02596582,-0.04183722,-0.01532005,-0.00403448,-0.04775461,0.04760872,-0.03179387,-0.03351535,0.09989609,-0.03884643,-0.0191818,0.00741841,0.00415571,0.00170406,-0.03148035,-0.00896246,-0.03032231,0.00309976,0.00603706,-0.00021828,-0.07253471,0.06294403,0.01532039,-0.04007537,0.04381241,-0.01452344,0.01452257,-0.01812029,-0.0354827,0.05090613,-0.05623763,-0.0425655,-0.01216864,0.04014374,-0.01368945,-0.04632344,0.02657743,0.06862865,0.00492705,-0.04386735,0.00788643,0.01683437,0.00438234,0.04589531,-0.02185147,-0.03970124,0.03664735,-0.05842838,0.04585875,0.03367979,0.05309311,0.05549265,0.01059647,-0.0292065,-0.05117016,0.03483986,-0.07836009,0.00272432,-0.02129231,0.00163082,0.0191407,-0.05788035,0.01708502,0.00478933,0.08858173,0.01506673,-0.00642266,-0.00080596,0.01825253,-0.01244267,-0.00646572,-0.04025996,-0.02506644,-0.03562029,0.00553377,-0.01209902,0.00698299,-0.03612584,-0.00073025,-0.08670165,-0.02866606,0.02700509,-0.013553,0.00512218,-0.02343111,-0.08985276,0.03589717,0.00918254,-0.02394183,0.02135396,0.04616963,-0.02987261,-0.05108006,-0.03284829,0.00234632,0.01240675,-0.0292163,-0.03398957,-0.00949007,-0.04522681,-0.00355067,0.01200221,0.05600904,0.03032476,0.00391895,-0.05970325,0.01483133,-0.00695793,-0.02776962,-0.01642221,0.01061434,-0.01083884,-0.02971298,0.01859747,0.06196244,-0.02242299,-0.02635903,-0.0260587,0.0334941,-0.02732237,-0.03355099,-0.03609686,0.03130561,-0.01575137,-0.09401807,-0.08651065,-0.03442237,0.02552913,-0.04373706,0.03666835,0.03378532,-0.03722576,0.02757626,0.01785807,-0.0231942,-0.03909735,0.01200322,-0.04751596,0.06569582,0.00274864,0.0011452,0.06414485,-0.05929947,0.02550132,-0.05965795,-0.03491802,0.01158841,0.01778333,0.03668978,0.04440834,0.0456317,0.01723261,0.00580292,-0.07786305,-0.00105387,0.03589536,0.01103601,-0.01396947,0.02974416,0.06274501,-0.01583959,-0.01539226,-0.03768888,0.00750834,-0.05009186,0.02040165,-0.03216485,-0.01092729,-0.02200207,0.02093994,-0.00825764,-0.0502831,0.04702481,0.01430725,0.06848351,0.01776907,-0.06201132,0.02053004,-0.02761917,0.03575118,0.07253958,0.09889359,-0.0294085,-0.08028408,-0.01017542,-0.01116564,-0.02014502,-0.02475045,-0.04563956,0.00322074,0.00259655,-0.01801171,0.01604126,-0.03997987,0.0223921,-0.01948963,-0.02515811,-0.04079442,0.02667291,0.01894617,0.02121269,0.0656941,-0.00018626,0.01406068,-0.03157528,-0.00346071,-0.02121136,0.01848905,-0.00287314,-0.02247145,0.02242947,0.00005477,0.02893431,0.0053373,-0.06687507,0.0291579,-0.04623811,0.03290804,-0.03253253,-0.0289335,-0.02952755,-0.00078289,0.00949271,0.00846699,0.02782743,-0.01922908,-0.01430985,0.04040582,0.03410619,0.01991061,0.00987072,-0.02755969,0.01019184,0.05796015,-0.00556551,0.07497409,0.00559478,0.03206582,0.01079788,0.04256064,-0.00752467,0.02523564,-0.03211814,0.01129281,0.02885698,0.00033521,-0.02493105,-0.10122424,0.04534119,0.06729276,-0.03349476,-0.04096945,0.01861832,-0.02137109,0.00328637,-0.0391143,-0.02723124,0.03177579,-0.03992002,-0.0236821,-0.00929644,-0.00216089,-0.00627596,0.06428131,0.0644339,-0.02415761,0.01236717,0.04904501,0.00786107,-0.01740758,0.03868477,-0.04245523,0.05052625,-0.01805205,-0.03639427,0.00957282,0.0678973,0.03668696,0.0212207,0.02639637,-0.03416235,-0.04734375,0.03060632,-0.00173096,0.04584288,-0.04866594,0.01711136,0.02542523,-0.00907456,-0.02554777,-0.0719875,0.01224362,0.00094039,-0.02066652,-0.0169351,0.02705869,-0.0251141,0.05217817,-0.02075532,-0.02114098,-0.05627083,-0.0177023,-0.05023849,0.01553982,0.01912215,0.01716426,-0.09973046,-0.00669668,0.04264766,0.00525551,-0.02718896,-0.03272559,0.00206509,-0.03101002,0.02838905,-0.02975033,-0.04954916,0.01078478,0.03059294,0.03003366,-0.01375275,-0.0269296,0.02881795,-0.0141618,0.0288404,-0.01109782,-0.01122385,-0.01363245,0.03988766,-0.00382613,-0.05477619,-0.03828835,-0.00864971,0.01684636,-0.01290889,-0.00574674,-0.00888846,-0.02260434,0.01509515,-0.00896071,0.01873128,-0.08764067,-0.02375737,-0.06041502,0.03887733,0.08231206,-0.0012128,0.01114086,0.02218309,0.01073407,0.02498491,-0.06036405,0.00103587,0.0319187,-0.0256715,-0.01882073,-0.02679375,-0.09173881,-0.05579073,-0.00044052,0.04985669,0.04833869,0.00428045,0.01977818,0.08741095,-0.05333288,-0.0931866,-0.03845604,-0.02285455,0.02107195,0.00879495,0.05452628,-0.00239397,-0.01060338,-0.01217621,-0.00665552,0.0196043,-0.01803421,-0.01668024,-0.01066082,0.03236762,-0.08438335,-0.02992983,-0.05195423,0.07162151,-0.01357601,0.01239188,-0.02693165,-0.00015315,0.00987349,-0.03411942,0.05432227,-0.03305979,0.00772839,0.01682854,0.01871999,-0.00501144,-0.04266871,-0.04333169,-0.00869256,-0.03134163,0.02253212,-0.01812511,0.02014221,-0.04607848,0.03925677,-0.04505574,0.03988201,-0.04952984,0.01681272,-0.03101265,0.04047477,0.01786979,0.05331738,-0.03500645,-0.02852162,-0.05395988,0.01201816,0.01054368,0.02002557,0.02247208,-0.03099202,-0.00508527,0.00344835,0.01438661,-0.00446643,-0.03775213,0.03465395,0.00559729,0.01649107,-0.04329969,-0.04675261,0.0240861,-0.07502673,-0.00751902,-0.00414801,-0.02887551,0.00930545,0.00792167,0.01552128,-0.03439556,-0.03337026,0.01431616,0.01335149,0.04547683,0.02099236,-0.05714704,0.05614916,-0.01047657,0.01190095,0.00381078,-0.04193554,-0.02594291,0.07438716,-0.0373937,0.00536969,0.02083164,0.01526951,0.00364352,0.01386771,-0.00115169,-0.04018045,0.00237867,0.00293076,-0.00012944,0.02011675,-0.01741681,-0.06861648,0.00422494,0.05688664,0.04405415,0.00086157,0.04937932,-0.00224484,0.0572032,0.00497519,0.06432024,0.00129851,-0.03404829,0.03209551,-0.03011315,0.07311642,0.01098597,0.00136017,0.03831398,0.01087097,0.04607769,0.06012515,0.01240242,0.10619385,-0.01043391,0.0672572,0.00048686,-0.05386274,0.0000011,-0.0423845,0.03961587,0.05672049,-0.0166736,-0.01031823,-0.01556023,-0.06149543,-0.00398294,-0.01129679],"last_embed":{"tokens":254,"hash":"eypkxl"}}},"last_read":{"hash":"eypkxl","at":1751441883000},"class_name":"SmartSource","outlinks":[],"metadata":{"aliases":["file upload","文件上传漏洞"],"tags":["网络安全/漏洞/文件上传"],"类型":["漏洞"],"文档更新日期":"2023-12-20"},"blocks":{"#---frontmatter---":[1,10],"#简介":[11,20],"#简介#{1}":[12,13],"#简介#{2}":[14,15],"#简介#{3}":[16,19],"#简介#{4}":[20,20],"#漏洞形成的原因":[21,37],"#漏洞形成的原因#{1}":[23,23],"#漏洞形成的原因#{2}":[24,24],"#漏洞形成的原因#{3}":[25,25],"#漏洞形成的原因#{4}":[26,28],"#漏洞形成的原因#{5}":[29,33],"#漏洞形成的原因#{6}":[34,37]},"last_import":{"mtime":1715930118181,"size":1046,"at":1749024987637,"hash":"eypkxl"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用分析.md","last_embed":{"hash":"eypkxl","at":1751441883000}},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用分析.md#---frontmatter---": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用分析.md#---frontmatter---","lines":[1,10],"size":96,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用分析.md#简介": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用分析.md#简介","lines":[11,20],"size":189,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用分析.md#简介#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用分析.md#简介#{1}","lines":[12,13],"size":68,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用分析.md#简介#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用分析.md#简介#{2}","lines":[14,15],"size":46,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用分析.md#简介#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用分析.md#简介#{3}","lines":[16,19],"size":64,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用分析.md#简介#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用分析.md#简介#{4}","lines":[20,20],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用分析.md#漏洞形成的原因": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用分析.md#漏洞形成的原因","lines":[21,37],"size":191,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用分析.md#漏洞形成的原因#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用分析.md#漏洞形成的原因#{1}","lines":[23,23],"size":14,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用分析.md#漏洞形成的原因#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用分析.md#漏洞形成的原因#{2}","lines":[24,24],"size":10,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用分析.md#漏洞形成的原因#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用分析.md#漏洞形成的原因#{3}","lines":[25,25],"size":12,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用分析.md#漏洞形成的原因#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用分析.md#漏洞形成的原因#{4}","lines":[26,28],"size":53,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用分析.md#漏洞形成的原因#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用分析.md#漏洞形成的原因#{5}","lines":[29,33],"size":79,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用分析.md#漏洞形成的原因#{6}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用分析.md#漏洞形成的原因#{6}","lines":[34,37],"size":7,"outlinks":[],"class_name":"SmartBlock"},
"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用分析.md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用分析.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09462222,-0.01923768,-0.01612997,-0.04749021,0.04948711,-0.01019118,-0.04289489,0.00587963,0.03327803,0.00040269,0.04075101,-0.03911361,0.05893571,0.04393715,0.05200091,0.02516581,0.02502154,-0.02558608,-0.03734597,0.00654109,0.08166409,-0.04439339,0.00682757,-0.05333884,0.00467805,-0.0134442,0.00656943,-0.01227648,-0.02542433,-0.15531076,-0.0078654,-0.01973325,0.02759171,0.00970274,-0.01866695,-0.05606939,0.04578136,0.05381802,0.00584874,-0.00575111,-0.01691345,0.0516751,0.00354944,-0.03920261,-0.03977687,-0.02841358,-0.05713505,-0.03215012,0.02434182,-0.04092974,-0.0487265,-0.04857851,-0.04235604,-0.01976423,-0.06045689,-0.02508279,0.00283022,-0.00439284,0.03069128,0.01643036,0.02265421,0.02854977,-0.1990931,0.06424934,0.02254166,-0.03971618,-0.04635021,0.00233898,0.01400838,0.06156438,-0.09865025,0.04994658,-0.01919802,0.0652005,0.06007127,-0.03862051,0.04696644,-0.01831668,-0.06201693,-0.05958141,-0.02871979,0.04752,-0.04099698,0.00790129,-0.00367671,0.02153619,-0.01145183,-0.02494825,-0.04111372,-0.00412662,0.00502263,-0.02625936,-0.02126791,0.03318124,-0.00114821,-0.00289558,0.01482597,0.06191507,-0.04826097,0.11423076,-0.09203974,0.00041009,0.00567626,-0.06804495,-0.01599133,-0.02906409,0.00112934,-0.01704689,0.02971924,-0.00847941,-0.04811572,-0.03360438,0.0508758,-0.02845177,0.05279443,0.03693878,0.00117883,-0.00300957,-0.03879887,-0.04789023,-0.01402239,-0.00223272,0.08103579,-0.02360463,-0.03516716,-0.0263861,0.06407425,0.05823267,0.03678267,0.02977201,0.06293312,0.00273813,-0.06891716,-0.04092304,-0.01216241,-0.00208569,-0.03196581,0.01790089,-0.02909747,-0.03050356,-0.01718522,-0.06854135,-0.00360346,-0.10006005,-0.07995033,0.08737714,-0.05993288,-0.02771408,0.07191657,-0.0513106,-0.00236555,0.09264424,-0.03413571,-0.01481069,-0.0071268,-0.00980522,0.08809084,0.12800789,-0.03662334,-0.01771437,-0.00085336,0.02628954,-0.08406401,0.16950743,0.01132386,-0.07827845,-0.02187753,-0.00524164,0.0375329,-0.02286885,0.03153723,-0.01828952,0.03946576,-0.00326367,0.0708657,-0.0055179,0.00652501,-0.05239879,0.02096706,0.03827751,0.09810815,-0.02046459,-0.07712024,0.07316347,-0.01262238,-0.07859957,-0.06085514,-0.00415076,0.02117497,-0.02269131,-0.1032545,0.05309033,-0.03799171,-0.01959885,-0.05601636,-0.06278994,0.03235475,0.02005653,0.01850383,-0.0725852,0.10052647,0.03094778,-0.01641498,0.01717395,-0.0178366,-0.01373302,0.01289952,-0.00579233,0.02050531,0.00787058,0.01120474,-0.0234531,-0.009392,-0.00518439,-0.02946299,0.05316844,0.0265153,0.00030626,0.02642563,0.08449998,-0.00240875,-0.06495261,-0.03968984,-0.19600046,-0.00666366,0.0034163,-0.04103468,0.00652498,-0.00890556,0.04458727,0.0341438,0.09804133,0.10196066,0.07107262,0.01453052,-0.08934161,-0.02599818,0.0075769,-0.02639434,0.01796004,-0.01342915,-0.02890557,0.01038512,0.00621025,0.04453264,-0.02355314,-0.02106496,0.0802526,-0.0330886,0.12708603,0.05511045,0.0113752,0.04462953,0.05581607,0.05892207,-0.00523364,-0.11053745,0.03734174,0.00869461,-0.03701425,-0.04182768,-0.0265129,-0.02658111,0.03067763,0.00847834,-0.02807629,-0.07202344,-0.01894101,-0.01722175,-0.02378266,0.01634025,0.03535344,0.02358543,0.00060117,-0.00752752,0.00972855,0.0095125,-0.03543029,-0.05395663,-0.03585064,0.00281775,-0.01642126,0.01120958,0.07051551,-0.00779701,0.04984626,0.02247521,-0.01473971,-0.03200826,-0.01717201,-0.02668327,-0.0726604,-0.00144987,0.00784383,0.14605998,-0.01560402,-0.01541393,0.03281747,0.01441455,0.00844849,-0.01070024,0.0065837,-0.01361854,0.02889049,0.0311453,0.04175335,0.02320294,-0.02532883,0.02492027,0.01749372,-0.02082945,0.08324821,-0.04473065,-0.04085613,-0.01150614,-0.05258248,0.02232081,0.0762811,-0.01688825,-0.31262717,0.02159448,-0.01277642,0.01733223,0.05872313,0.02642968,0.06659307,0.04226892,-0.013103,0.03806406,-0.06540496,0.04208437,-0.01726578,-0.04889084,-0.02546877,-0.03649322,0.02935713,0.01813185,0.09145683,-0.00626071,0.03082542,0.05178475,0.2209383,0.00149175,0.0461713,-0.00449667,-0.00312657,0.05110531,0.02337128,0.02512657,0.02029496,-0.04201565,0.04428285,-0.03725993,0.05392777,0.04089808,-0.0447799,0.03591456,0.00759623,0.01871804,-0.04293136,0.04380971,-0.0819955,0.03157792,0.07255892,0.03214758,-0.01894077,-0.02427264,-0.01927041,0.06192324,0.0001219,0.00765832,0.00276689,-0.01406635,0.02061214,0.03420734,0.03333508,-0.03735314,-0.06005366,-0.00908255,0.04969377,0.01268549,0.10078902,0.132881,0.08315642],"last_embed":{"hash":"0c3dd77631514e6dcd620057b55f3620267dcb89edc4aa9e2f8ac53cd08aa707","tokens":418}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.06924506,0.00110352,-0.02270507,-0.0249665,0.01115249,-0.02661869,0.06251435,0.02173182,-0.02865702,-0.03749422,-0.04676188,-0.02698586,-0.08796378,-0.05248044,0.05673917,0.07073528,0.00596425,0.01634131,0.06082423,-0.03384754,-0.02640466,0.0537894,0.05903397,-0.00994853,-0.02128109,-0.00097565,0.00025795,-0.02560222,0.03296933,0.07334772,0.07950655,-0.02560655,0.02574745,0.01481024,-0.05764888,-0.05946704,0.02241926,-0.03781897,-0.00349531,0.01610952,-0.03572265,0.007619,-0.00445859,0.03981923,-0.04844574,-0.01102196,-0.02839859,-0.03193638,-0.00924423,-0.00077965,0.03851888,0.02836069,-0.01450542,0.01675958,0.01100974,0.04608507,-0.0310591,-0.05091761,-0.00072057,-0.01214599,0.02589943,-0.04637446,0.06972103,0.03146876,0.02496338,-0.00633287,-0.01850403,0.0000494,-0.01477005,-0.03288736,-0.05520573,-0.03961279,0.0044985,0.04367771,0.06684505,-0.01501284,0.06730701,0.00057342,0.04661007,0.02795289,-0.06314786,0.02279959,-0.01743706,0.02145857,-0.01419951,0.01282807,0.09860677,-0.07026435,0.04818104,-0.0020036,-0.00763328,0.00000779,-0.0245968,-0.06087473,0.04204957,-0.02073602,0.05674352,-0.01365962,0.01988637,-0.01318354,-0.01223954,0.00150893,-0.06518818,-0.00365342,-0.03361795,-0.09563023,0.00868438,0.0526514,-0.02676688,0.00367405,0.00790611,0.01273345,-0.0311335,0.02944743,0.02424596,0.05236,-0.03755531,0.00574103,0.00633902,0.05161665,0.01226062,0.04212836,0.03270948,-0.03990059,-0.04126802,0.00164382,-0.03877624,-0.02905175,0.03947436,0.02538512,-0.01680165,0.04926629,-0.03546347,-0.02383007,0.05703918,0.01562143,0.02321992,-0.03996455,0.0622747,-0.0478425,0.02020326,-0.02160926,-0.02903677,0.00749199,-0.04169759,0.01182613,-0.02145981,-0.06020745,-0.05669768,0.04890424,0.06070299,0.0223171,-0.04111281,-0.04535325,-0.00624742,-0.0160103,-0.00064713,0.01293562,0.02105995,-0.00803859,-0.00461583,0.04111095,0.00036996,-0.00523682,-0.01584904,-0.00452164,0.01257817,0.04184115,0.01566415,0.02589862,0.00959883,0.02431069,-0.01051132,0.02326974,0.01975343,-0.0602612,0.05899235,0.05059131,-0.0350797,-0.02596804,0.03317826,-0.05436733,-0.01157512,0.05444827,0.01936599,0.02481391,-0.04097705,0.02131812,0.00823432,0.00810703,0.04841996,0.032445,0.01715794,0.0334444,-0.01387007,0.02316258,0.02398351,-0.08632343,-0.02297284,0.03525326,0.02162785,-0.00250841,-0.0278463,-0.08443438,0.01334443,-0.02528349,-0.02329516,-0.0046792,-0.03291419,0.06943197,-0.05109219,-0.00127841,0.01125223,0.01869241,-0.03941694,0.03262444,-0.01442604,-0.00197173,0.0147463,-0.01298351,0.00769374,-0.04462582,-0.07145239,-0.02329507,-0.05539097,-0.02288734,-0.01205948,0.00049156,0.03706796,0.00785159,-0.04335263,0.00604716,-0.0238617,-0.00098843,-0.01826673,-0.01595442,0.06263755,0.01722294,0.01152954,0.00349598,0.01193602,0.04441283,0.02501122,-0.00310662,-0.02815284,-0.03030094,-0.04380113,0.04297959,0.01266471,0.01874606,0.01693622,0.03009495,-0.00927284,0.04429941,-0.00867295,-0.07492802,-0.02036307,-0.00800475,-0.00777409,-0.03598901,0.03370573,-0.01133873,-0.06033148,0.01476286,-0.02596582,-0.04183722,-0.01532005,-0.00403448,-0.04775461,0.04760872,-0.03179387,-0.03351535,0.09989609,-0.03884643,-0.0191818,0.00741841,0.00415571,0.00170406,-0.03148035,-0.00896246,-0.03032231,0.00309976,0.00603706,-0.00021828,-0.07253471,0.06294403,0.01532039,-0.04007537,0.04381241,-0.01452344,0.01452257,-0.01812029,-0.0354827,0.05090613,-0.05623763,-0.0425655,-0.01216864,0.04014374,-0.01368945,-0.04632344,0.02657743,0.06862865,0.00492705,-0.04386735,0.00788643,0.01683437,0.00438234,0.04589531,-0.02185147,-0.03970124,0.03664735,-0.05842838,0.04585875,0.03367979,0.05309311,0.05549265,0.01059647,-0.0292065,-0.05117016,0.03483986,-0.07836009,0.00272432,-0.02129231,0.00163082,0.0191407,-0.05788035,0.01708502,0.00478933,0.08858173,0.01506673,-0.00642266,-0.00080596,0.01825253,-0.01244267,-0.00646572,-0.04025996,-0.02506644,-0.03562029,0.00553377,-0.01209902,0.00698299,-0.03612584,-0.00073025,-0.08670165,-0.02866606,0.02700509,-0.013553,0.00512218,-0.02343111,-0.08985276,0.03589717,0.00918254,-0.02394183,0.02135396,0.04616963,-0.02987261,-0.05108006,-0.03284829,0.00234632,0.01240675,-0.0292163,-0.03398957,-0.00949007,-0.04522681,-0.00355067,0.01200221,0.05600904,0.03032476,0.00391895,-0.05970325,0.01483133,-0.00695793,-0.02776962,-0.01642221,0.01061434,-0.01083884,-0.02971298,0.01859747,0.06196244,-0.02242299,-0.02635903,-0.0260587,0.0334941,-0.02732237,-0.03355099,-0.03609686,0.03130561,-0.01575137,-0.09401807,-0.08651065,-0.03442237,0.02552913,-0.04373706,0.03666835,0.03378532,-0.03722576,0.02757626,0.01785807,-0.0231942,-0.03909735,0.01200322,-0.04751596,0.06569582,0.00274864,0.0011452,0.06414485,-0.05929947,0.02550132,-0.05965795,-0.03491802,0.01158841,0.01778333,0.03668978,0.04440834,0.0456317,0.01723261,0.00580292,-0.07786305,-0.00105387,0.03589536,0.01103601,-0.01396947,0.02974416,0.06274501,-0.01583959,-0.01539226,-0.03768888,0.00750834,-0.05009186,0.02040165,-0.03216485,-0.01092729,-0.02200207,0.02093994,-0.00825764,-0.0502831,0.04702481,0.01430725,0.06848351,0.01776907,-0.06201132,0.02053004,-0.02761917,0.03575118,0.07253958,0.09889359,-0.0294085,-0.08028408,-0.01017542,-0.01116564,-0.02014502,-0.02475045,-0.04563956,0.00322074,0.00259655,-0.01801171,0.01604126,-0.03997987,0.0223921,-0.01948963,-0.02515811,-0.04079442,0.02667291,0.01894617,0.02121269,0.0656941,-0.00018626,0.01406068,-0.03157528,-0.00346071,-0.02121136,0.01848905,-0.00287314,-0.02247145,0.02242947,0.00005477,0.02893431,0.0053373,-0.06687507,0.0291579,-0.04623811,0.03290804,-0.03253253,-0.0289335,-0.02952755,-0.00078289,0.00949271,0.00846699,0.02782743,-0.01922908,-0.01430985,0.04040582,0.03410619,0.01991061,0.00987072,-0.02755969,0.01019184,0.05796015,-0.00556551,0.07497409,0.00559478,0.03206582,0.01079788,0.04256064,-0.00752467,0.02523564,-0.03211814,0.01129281,0.02885698,0.00033521,-0.02493105,-0.10122424,0.04534119,0.06729276,-0.03349476,-0.04096945,0.01861832,-0.02137109,0.00328637,-0.0391143,-0.02723124,0.03177579,-0.03992002,-0.0236821,-0.00929644,-0.00216089,-0.00627596,0.06428131,0.0644339,-0.02415761,0.01236717,0.04904501,0.00786107,-0.01740758,0.03868477,-0.04245523,0.05052625,-0.01805205,-0.03639427,0.00957282,0.0678973,0.03668696,0.0212207,0.02639637,-0.03416235,-0.04734375,0.03060632,-0.00173096,0.04584288,-0.04866594,0.01711136,0.02542523,-0.00907456,-0.02554777,-0.0719875,0.01224362,0.00094039,-0.02066652,-0.0169351,0.02705869,-0.0251141,0.05217817,-0.02075532,-0.02114098,-0.05627083,-0.0177023,-0.05023849,0.01553982,0.01912215,0.01716426,-0.09973046,-0.00669668,0.04264766,0.00525551,-0.02718896,-0.03272559,0.00206509,-0.03101002,0.02838905,-0.02975033,-0.04954916,0.01078478,0.03059294,0.03003366,-0.01375275,-0.0269296,0.02881795,-0.0141618,0.0288404,-0.01109782,-0.01122385,-0.01363245,0.03988766,-0.00382613,-0.05477619,-0.03828835,-0.00864971,0.01684636,-0.01290889,-0.00574674,-0.00888846,-0.02260434,0.01509515,-0.00896071,0.01873128,-0.08764067,-0.02375737,-0.06041502,0.03887733,0.08231206,-0.0012128,0.01114086,0.02218309,0.01073407,0.02498491,-0.06036405,0.00103587,0.0319187,-0.0256715,-0.01882073,-0.02679375,-0.09173881,-0.05579073,-0.00044052,0.04985669,0.04833869,0.00428045,0.01977818,0.08741095,-0.05333288,-0.0931866,-0.03845604,-0.02285455,0.02107195,0.00879495,0.05452628,-0.00239397,-0.01060338,-0.01217621,-0.00665552,0.0196043,-0.01803421,-0.01668024,-0.01066082,0.03236762,-0.08438335,-0.02992983,-0.05195423,0.07162151,-0.01357601,0.01239188,-0.02693165,-0.00015315,0.00987349,-0.03411942,0.05432227,-0.03305979,0.00772839,0.01682854,0.01871999,-0.00501144,-0.04266871,-0.04333169,-0.00869256,-0.03134163,0.02253212,-0.01812511,0.02014221,-0.04607848,0.03925677,-0.04505574,0.03988201,-0.04952984,0.01681272,-0.03101265,0.04047477,0.01786979,0.05331738,-0.03500645,-0.02852162,-0.05395988,0.01201816,0.01054368,0.02002557,0.02247208,-0.03099202,-0.00508527,0.00344835,0.01438661,-0.00446643,-0.03775213,0.03465395,0.00559729,0.01649107,-0.04329969,-0.04675261,0.0240861,-0.07502673,-0.00751902,-0.00414801,-0.02887551,0.00930545,0.00792167,0.01552128,-0.03439556,-0.03337026,0.01431616,0.01335149,0.04547683,0.02099236,-0.05714704,0.05614916,-0.01047657,0.01190095,0.00381078,-0.04193554,-0.02594291,0.07438716,-0.0373937,0.00536969,0.02083164,0.01526951,0.00364352,0.01386771,-0.00115169,-0.04018045,0.00237867,0.00293076,-0.00012944,0.02011675,-0.01741681,-0.06861648,0.00422494,0.05688664,0.04405415,0.00086157,0.04937932,-0.00224484,0.0572032,0.00497519,0.06432024,0.00129851,-0.03404829,0.03209551,-0.03011315,0.07311642,0.01098597,0.00136017,0.03831398,0.01087097,0.04607769,0.06012515,0.01240242,0.10619385,-0.01043391,0.0672572,0.00048686,-0.05386274,0.0000011,-0.0423845,0.03961587,0.05672049,-0.0166736,-0.01031823,-0.01556023,-0.06149543,-0.00398294,-0.01129679],"last_embed":{"tokens":254,"hash":"eypkxl"}}},"last_read":{"hash":"eypkxl","at":1751597376251},"class_name":"SmartSource","outlinks":[],"metadata":{"aliases":["file upload","文件上传漏洞"],"tags":["网络安全/漏洞/文件上传"],"类型":["漏洞"],"文档更新日期":"2023-12-20"},"blocks":{"#---frontmatter---":[1,10],"#简介":[11,20],"#简介#{1}":[12,13],"#简介#{2}":[14,15],"#简介#{3}":[16,19],"#简介#{4}":[20,20],"#漏洞形成的原因":[21,37],"#漏洞形成的原因#{1}":[23,23],"#漏洞形成的原因#{2}":[24,24],"#漏洞形成的原因#{3}":[25,25],"#漏洞形成的原因#{4}":[26,28],"#漏洞形成的原因#{5}":[29,33],"#漏洞形成的原因#{6}":[34,37]},"last_import":{"mtime":1715930118181,"size":1046,"at":1749024987637,"hash":"eypkxl"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用分析.md","last_embed":{"hash":"eypkxl","at":1751597376251}},