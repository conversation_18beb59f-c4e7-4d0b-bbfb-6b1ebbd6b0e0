"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/内核漏洞.md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/内核漏洞.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05358155,-0.01312906,0.00156708,-0.04952252,0.00893169,-0.03931631,-0.04369886,0.05339142,0.06428578,0.01600038,-0.01785382,-0.04864735,0.03445239,0.02339755,0.06788499,0.00813204,-0.01414861,0.05155688,0.00680607,-0.01672927,0.0820921,-0.05272396,-0.00835009,-0.07651821,-0.0493518,0.02205146,0.05104792,-0.04321992,-0.02631167,-0.17128786,0.01100596,0.01686223,0.02475506,0.01938671,-0.01905306,-0.05892304,0.03662207,0.02709734,-0.01193882,0.04287376,0.01519255,0.0317115,-0.01610282,-0.013231,-0.02529181,-0.06968073,-0.02402555,-0.0479857,0.01939651,-0.04043742,-0.03176372,-0.01579155,-0.03311148,-0.03062403,-0.0738217,-0.00871412,0.06189306,0.00889221,0.08342336,0.0072847,0.01320381,0.02096551,-0.20666713,0.04603849,0.00710953,-0.0354446,-0.00116128,-0.06713896,0.0147512,0.04492844,-0.02590721,0.05553513,-0.01916211,0.05313798,0.05015668,-0.00558071,0.01803007,-0.01994981,-0.04431949,-0.0208421,-0.00517966,0.04511742,-0.03204511,-0.02065965,-0.01297143,0.01681272,-0.0267577,0.01991683,0.04244586,0.01339175,-0.01721239,-0.07501399,0.02444548,0.03854265,0.0063688,0.02550348,0.04712979,0.05144352,-0.12064873,0.1163615,-0.04097199,-0.01612177,-0.00584871,-0.0515734,0.0105049,-0.04226943,0.00032196,-0.0028881,-0.02650802,-0.02789971,-0.04914915,-0.02764443,0.06469857,-0.01128209,0.08408971,-0.00445257,0.02281048,-0.02907097,-0.01177514,-0.01403161,-0.01447301,0.02781706,0.0640824,-0.0261987,0.00692975,-0.03147582,0.00907829,0.07606094,0.0111802,0.02820915,0.05207605,-0.02853617,-0.05957386,-0.02854117,-0.00101441,0.01433331,-0.05371933,0.03698432,0.00228384,-0.06987387,-0.03695309,-0.02758328,0.03021433,-0.09022846,-0.05626172,0.11138183,-0.04969665,-0.01603439,0.01229987,-0.00273502,0.02310153,0.06421131,-0.01498333,-0.03054856,-0.01315821,-0.00957763,0.07992749,0.12300994,-0.04207082,-0.02249489,-0.03637811,0.00936702,-0.0804271,0.17225623,0.0306046,-0.03120972,-0.03028871,0.00341127,0.03149262,-0.02621971,0.00139036,-0.03720346,0.03064333,-0.01033582,0.05780094,-0.00335199,-0.05222708,-0.00829269,0.00258131,0.04151149,0.0912597,-0.00320107,-0.09696589,0.05031845,0.02570757,-0.10346621,-0.02306679,-0.05381366,-0.00494265,-0.04826039,-0.12845039,0.04576437,-0.02116624,-0.01723805,-0.05791348,-0.0621721,0.03177182,0.0216132,0.0430426,-0.04246716,0.06913848,0.05206418,-0.04987809,-0.00426294,-0.05308561,-0.03696131,-0.02056682,-0.02250074,0.0118702,0.048926,-0.0018747,0.00983406,-0.00852631,0.00054145,-0.03796707,0.01641528,0.00742581,0.02546614,0.04102467,0.05051238,0.02883923,-0.02578483,-0.0891353,-0.23842314,-0.05341277,-0.00179518,-0.03526008,-0.01104837,0.01690703,0.06373964,-0.00166581,0.08843647,0.03987243,0.06269509,0.03898746,-0.08213872,-0.0242338,0.02999165,-0.01222337,0.02023302,-0.02279404,-0.02157511,-0.01305494,0.008016,0.06421195,0.01702051,-0.0244647,0.02151805,-0.0369279,0.10282025,0.02755967,0.04294964,0.00402343,0.01613213,0.05678491,0.05902576,-0.10012782,0.0178872,0.05317872,-0.0339121,-0.05399514,-0.0256958,-0.00063151,-0.00179957,-0.00142407,-0.03479642,-0.07508181,0.0059065,-0.04341796,-0.00302364,0.00633372,0.01536079,0.07299445,-0.02058321,0.00004288,0.00921437,0.01450171,0.01961464,-0.02470414,-0.03834438,-0.03286775,-0.01173338,0.01777318,0.02139918,-0.0430848,0.03581772,0.00406499,0.02021032,-0.00000773,-0.01952489,-0.02316465,-0.03428125,-0.020919,-0.0463232,0.1601775,-0.00341816,-0.05301923,0.05882331,0.0408255,0.00232807,-0.0296459,0.02405517,0.00056593,0.06382171,0.0198861,0.04768824,0.02306333,0.00780909,0.02129327,0.01817328,0.00522342,0.05685252,-0.10324256,-0.0732756,-0.01155169,-0.03612365,0.0167531,0.12101689,-0.02221199,-0.28589723,0.01792958,0.01406411,-0.0174273,0.08544792,0.03340222,0.03532151,-0.03118969,-0.05226356,0.05150674,-0.06004403,0.06621376,-0.01625788,-0.01304014,-0.01489436,-0.02252879,0.08003294,-0.01337484,0.06865445,-0.01347863,-0.01299561,0.03557369,0.1906282,-0.01116831,0.03228542,-0.00197234,-0.0004852,0.0675877,0.068501,0.05211864,0.01875694,-0.07784592,0.02770222,-0.02591028,0.03011863,0.06733069,-0.04137456,0.00685235,0.06535675,0.03480742,-0.00409002,0.0566589,-0.06483378,0.0046652,0.07460179,-0.00135276,0.01133332,-0.05351216,0.01025859,0.016583,0.04548194,0.02874292,0.01112345,0.00258428,0.04579295,0.04944383,-0.01202577,-0.06531165,-0.06795874,-0.01008993,0.03631497,0.01856832,0.08021317,0.12568954,0.05117951],"last_embed":{"hash":"0064b64b4e22555cef410bfde8d1e00999f75f5d65af5e4228850af4f553ff39","tokens":469}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.05333147,0.0018451,-0.00248332,0.01198994,-0.02537936,0.02691359,0.06181386,-0.03156469,0.01206418,-0.01551154,-0.04315084,0.01647417,-0.05465923,-0.07330397,0.01833646,0.00507848,0.02411768,0.03266681,0.03400226,-0.00884039,-0.00831277,0.04624414,0.04173995,0.05044678,0.0276554,-0.01512118,0.00728194,-0.04118609,0.03822619,0.0596975,0.09595629,0.0190624,0.00972927,-0.03086336,-0.07517681,-0.00269778,0.04528467,-0.04730415,-0.02872906,0.06845364,-0.01715408,0.02428183,-0.02455204,0.03874985,-0.10125837,-0.00226738,0.01615766,-0.0315009,-0.0048236,0.0206517,0.04907412,-0.09155727,0.02681989,0.03592482,-0.02261918,0.04560085,-0.05892429,-0.03355845,-0.00555189,-0.00313129,0.02539265,-0.00566111,0.07874683,0.03259515,0.02156402,0.04876103,-0.02094185,-0.04714283,0.02041209,-0.00390263,-0.05228562,0.02559326,0.00317433,-0.00318137,0.08479617,0.02245472,0.01928797,0.00779645,-0.00439055,-0.01452368,-0.0890471,-0.01173974,-0.01153873,0.07734451,0.05771377,-0.06351236,0.0424228,-0.02007562,-0.00003453,-0.02614598,-0.01276194,0.03161467,-0.0253702,-0.00714967,0.04023343,-0.06287785,0.01182628,0.00403306,-0.01293313,-0.02802814,-0.0301969,-0.02336662,-0.08198787,0.01535975,-0.03280049,-0.06055079,-0.05404438,0.05255889,-0.02901307,-0.02499366,0.02870555,0.0259639,-0.02231818,-0.00700388,-0.07049978,0.02772495,-0.03066375,-0.01352617,0.03098327,0.04706482,0.03359612,0.03818467,0.02736834,-0.04546658,-0.03236134,-0.02218967,-0.0128881,-0.02777902,0.05753998,-0.04597951,0.01687757,0.04340763,-0.05805218,-0.03612155,-0.02135622,0.03052905,0.04529762,-0.04750469,0.02988987,-0.02739136,0.01360927,-0.01929453,0.02129761,0.01897581,-0.05737205,0.0910839,-0.0418115,-0.02880305,-0.02886593,0.03763034,0.01568377,0.03943458,0.02478681,-0.05352493,0.01819518,-0.01616157,0.02589672,0.04181267,-0.00388917,-0.03319847,-0.01265999,0.01836059,0.02854737,0.01969635,0.02156096,-0.01463181,-0.03193012,0.0186465,0.00142586,0.02273412,-0.00637107,0.0200123,0.01807689,0.01024212,0.00329844,-0.04835032,0.01648805,0.0324863,-0.0175882,-0.0153377,0.02262558,-0.03869721,0.00047681,0.00259427,-0.00567048,0.05744601,-0.02748269,-0.01866112,0.05059767,0.03072117,0.03208617,0.02818546,-0.00278859,0.00591742,0.03090312,0.02691342,-0.03498084,-0.06260771,-0.02836096,0.02962423,0.0053079,-0.05151628,0.00868404,-0.07620119,0.06432216,-0.00275858,0.01296015,-0.06965821,-0.07948548,0.05387697,0.01987401,0.03280605,-0.00134598,0.01764691,-0.02950354,0.07988696,-0.03195756,0.03264022,0.04399384,-0.01503876,0.00483026,-0.02029087,-0.05267043,-0.0033538,-0.02745526,-0.03743507,0.02904322,0.0173481,0.01441615,-0.02996203,0.03831838,0.0477857,-0.03612622,0.00166306,-0.01845177,0.01498519,0.04723699,0.04761861,0.0247998,0.0060005,0.01530279,0.02591577,0.01091452,-0.01712857,-0.00107046,-0.04107106,-0.05858451,0.03800583,0.01525463,0.07209231,0.01936129,-0.04300072,-0.01124968,0.05970762,0.0303125,-0.03520101,-0.01403806,-0.02128436,0.0092171,-0.02354414,0.02005908,-0.00495462,-0.05758696,0.03015258,-0.01689168,-0.01957915,-0.03257001,0.02802654,-0.03955865,0.05209229,0.01850483,-0.02459934,0.06509848,0.00889577,-0.02860718,0.04572447,-0.02930032,0.01830087,-0.04186083,-0.0188087,-0.03932267,-0.02943519,-0.00325227,-0.01421194,-0.0535677,0.04437793,0.02034712,-0.01420281,0.02332432,0.00312457,0.04845908,0.00807812,-0.04560422,0.04504058,-0.08211749,-0.01725492,-0.01536471,-0.00029687,-0.03558782,-0.0678435,0.00874522,0.05718883,0.02843109,-0.00843778,-0.00236035,0.02872547,0.00329748,0.04622891,-0.01492961,-0.05413252,0.00487074,-0.05306622,0.01602327,-0.03318179,0.0235105,0.0931723,-0.00350372,0.01547996,-0.03897337,0.01242994,-0.07435647,0.027002,0.01496806,-0.02441656,0.01453073,-0.06904552,0.02642801,0.01826808,0.06399815,0.06382502,-0.01113345,0.00446543,0.01139754,-0.03089411,0.0295139,-0.00954349,-0.01523331,-0.04824262,0.02969405,-0.02087959,0.02216054,-0.04331579,-0.00190802,-0.03333127,0.01016661,0.06906681,-0.00574466,-0.00230533,-0.00669917,-0.02766736,0.02741657,-0.02541783,-0.00389838,-0.04452434,0.04157141,-0.01748176,-0.02550986,-0.02800817,-0.02020142,0.02275813,0.00073011,-0.03822097,-0.02464333,0.00083074,-0.05073337,0.03535783,0.03760049,0.04616416,-0.05047397,-0.07731213,0.03141756,0.00630879,-0.02605472,0.02690443,0.03803739,-0.01190009,-0.00925526,0.02211347,0.00027681,-0.04659389,-0.01949711,-0.05603274,-0.0103342,-0.0488103,-0.08044843,-0.05078195,-0.0087804,-0.03777409,-0.06512028,-0.04285901,-0.00837712,0.0302963,-0.04372116,0.01628857,0.00549078,0.02731753,0.04387075,-0.01104802,-0.01772155,-0.00977745,0.00692372,-0.02164836,0.03604868,0.01506941,0.00026673,0.06075535,-0.04257508,0.03449408,-0.04303076,-0.05337978,0.03087902,0.07027613,0.08152715,0.007551,-0.00435851,0.01082848,-0.00572109,-0.0595838,-0.00352582,0.06333196,0.0236926,-0.04621019,0.05267553,0.065741,0.02022762,0.01688771,-0.01763969,-0.02990569,0.00419162,0.0335909,-0.01347811,-0.03213925,-0.06261237,-0.02520429,-0.02648157,-0.01246015,0.06250867,-0.06063877,-0.01110357,0.07017461,-0.04923752,-0.00622294,0.03234601,-0.00115318,0.05547078,0.05164777,-0.02661395,-0.08814336,-0.00379725,-0.01083008,-0.0025545,0.02343026,-0.0772819,0.01600338,0.02861827,0.00861317,0.03103442,0.01505651,-0.01403094,-0.00415585,-0.02493643,-0.02305677,0.01018843,-0.02622472,0.0326774,0.01889149,0.04196811,0.01042262,-0.08038755,-0.00162523,-0.01131257,-0.01940829,-0.01426691,0.00667393,0.01829913,0.02822253,-0.0220632,0.01983538,-0.02148849,0.0252861,0.0326806,0.05327519,0.02681132,0.00694368,0.02836758,0.03663722,-0.04600719,0.04674131,0.05066982,-0.01048492,-0.01870051,0.03937332,0.00655646,0.04646242,0.00624215,-0.02715423,-0.02716072,0.05108683,0.01026107,0.01944566,-0.0250405,0.00544841,-0.01115619,-0.00676687,0.01213661,0.03045116,-0.02370268,-0.00756889,0.03643499,-0.01892626,-0.03593306,-0.00597461,0.08641185,0.06973408,-0.04658128,-0.06373017,-0.01733204,0.02467597,-0.00353293,0.01178216,-0.04046575,0.04070727,-0.06480456,0.00106543,0.0105133,-0.0538777,-0.0568415,-0.00185678,-0.00699863,-0.02489981,-0.06054397,-0.00331502,0.01680206,-0.00592898,0.03604392,-0.06460457,0.02991887,-0.05038331,-0.01437602,0.01243929,0.05353344,-0.03117455,-0.0023822,0.01708298,-0.03718093,-0.02370596,0.00142908,-0.03080332,0.07136675,-0.02812778,-0.00744878,0.00390622,0.02192166,0.00318795,-0.04575229,0.00148302,0.02260933,-0.05903727,-0.01853367,0.0606381,0.0195787,0.06993464,-0.06688304,0.03092643,-0.05581787,0.01634558,-0.04064677,0.01737352,0.00173699,0.03720163,-0.04749762,-0.02172976,0.05978216,-0.00792209,-0.03753277,0.01123036,0.0128878,-0.03174822,0.03186087,-0.02184123,0.00066939,0.01986306,0.02946448,-0.0010162,-0.00600238,0.00420386,0.0083614,0.0272919,0.01021323,0.01066105,0.00588432,0.00188581,-0.00442541,-0.03758782,-0.03157254,-0.01109463,-0.06404173,0.02003298,0.02864731,-0.03458195,0.00721358,0.02526936,-0.02000297,-0.02031828,0.01164103,-0.11494768,-0.04210066,-0.04686304,0.04262919,0.05059562,0.01100921,0.00985174,0.03566134,-0.02279787,0.0285936,-0.0266317,-0.00284693,-0.00176107,-0.01764244,0.0345173,-0.02331397,-0.07386454,-0.04302959,-0.00714793,-0.0123825,0.07293166,-0.01535174,0.01009841,0.06203523,0.00658072,-0.05552763,-0.04811829,0.00429624,0.05931644,-0.00888672,0.00038936,-0.05757725,0.00245932,0.00384555,-0.01816975,0.06702597,0.03192703,0.02155618,0.01693074,0.00861805,-0.06027753,-0.00787136,0.01539701,0.0393495,-0.03593403,0.00395824,-0.06094343,-0.00792905,0.00374128,-0.0157928,-0.00234496,0.00267588,0.00146822,0.01401502,0.08539803,-0.03062128,-0.03133949,-0.03552245,-0.02145004,-0.02550205,-0.00594406,0.07823745,-0.0016273,-0.05074373,0.03275183,-0.02725284,-0.01639772,-0.01790428,-0.00754477,-0.0316436,0.01944625,0.00887688,0.05149681,-0.02469523,-0.05039069,-0.02796737,-0.00004676,-0.00960048,-0.00994998,0.02644003,-0.03803654,-0.00772361,-0.02462978,-0.01158604,0.02052532,-0.06599044,0.03496331,0.01709654,-0.00176007,-0.02964099,-0.06621187,0.05272646,-0.05046444,0.01947185,0.00901941,-0.04288813,-0.03337726,-0.01520239,0.00627276,-0.05691971,-0.01058099,0.00949192,-0.01634897,0.046117,0.02505206,-0.05470738,0.04152668,-0.01009574,0.03741151,0.02022184,-0.03719804,-0.0007202,0.0997111,-0.09242322,0.01157021,-0.03409554,0.00571592,0.02742625,0.05142516,-0.06194679,-0.0463429,-0.02396964,-0.03284683,0.0241393,0.00479164,0.00375225,-0.0933897,0.04226168,0.00722213,0.03710624,0.00057601,0.00946709,-0.0027916,0.04542392,-0.00528394,0.0082302,0.01031614,-0.01665644,-0.0224717,-0.04018409,0.03723766,0.04984463,0.00695841,0.0432597,0.0361503,-0.02039021,0.03923188,-0.01207139,0.10243387,0.01316407,0.02267308,-0.01886138,-0.06064653,8.4e-7,0.01324562,-0.01012593,0.02609115,-0.01638514,-0.01054924,0.00339674,-0.02919086,-0.02477361,0.05085814],"last_embed":{"tokens":478,"hash":"164se62"}}},"last_read":{"hash":"164se62","at":1751441882831},"class_name":"SmartSource","outlinks":[{"title":"Kernel","target":"Kernel","line":6},{"title":"内核","target":"Kernel","line":17},{"title":"内核","target":"Kernel","line":19},{"title":"Linux","target":"Linux","line":19},{"title":"windows系统","target":"windows系统","line":19},{"title":"windows系统","target":"windows系统","line":21},{"title":"windows系统","target":"windows系统","line":21},{"title":"内存","target":"内存","line":30},{"title":"缓冲区溢出","target":"缓冲区溢出","line":31},{"title":"内核","target":"Kernel","line":32},{"title":"内核","target":"Kernel","line":32},{"title":"内核","target":"Kernel","line":34},{"title":"内核","target":"Kernel","line":34},{"title":"内核","target":"Kernel","line":36}],"metadata":{"aliases":null,"tags":["网络安全/漏洞"],"基础知识":["[[Kernel]]"],"cssclasses":["editor-full"]},"blocks":{"#---frontmatter---":[1,9],"#简介":[10,15],"#简介#{1}":[11,15],"#基于提权方面的考虑":[16,24],"#基于提权方面的考虑#{1}":[17,19],"#基于提权方面的考虑#{2}":[20,20],"#基于提权方面的考虑#{3}":[21,22],"#基于提权方面的考虑#{4}":[23,24],"#内核漏洞的主要类型":[25,42],"#内核漏洞的主要类型#{1}":[27,28],"#内核漏洞的主要类型#{2}":[29,30],"#内核漏洞的主要类型#{3}":[31,32],"#内核漏洞的主要类型#{4}":[33,34],"#内核漏洞的主要类型#{5}":[35,37],"#内核漏洞的主要类型#{6}":[38,40],"#内核漏洞的主要类型#{7}":[41,42]},"last_import":{"mtime":1747536242436,"size":1973,"at":1749024987637,"hash":"164se62"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/内核漏洞.md","last_embed":{"hash":"164se62","at":1751441882831}},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/内核漏洞.md#---frontmatter---": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/内核漏洞.md#---frontmatter---","lines":[1,9],"size":86,"outlinks":[{"title":"Kernel","target":"Kernel","line":6}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/内核漏洞.md#简介": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/内核漏洞.md#简介","lines":[10,15],"size":110,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/内核漏洞.md#简介#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/内核漏洞.md#简介#{1}","lines":[11,15],"size":105,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/内核漏洞.md#基于提权方面的考虑": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/内核漏洞.md#基于提权方面的考虑","lines":[16,24],"size":238,"outlinks":[{"title":"内核","target":"Kernel","line":2},{"title":"内核","target":"Kernel","line":4},{"title":"Linux","target":"Linux","line":4},{"title":"windows系统","target":"windows系统","line":4},{"title":"windows系统","target":"windows系统","line":6},{"title":"windows系统","target":"windows系统","line":6}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/内核漏洞.md#基于提权方面的考虑#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/内核漏洞.md#基于提权方面的考虑#{1}","lines":[17,19],"size":145,"outlinks":[{"title":"内核","target":"Kernel","line":1},{"title":"内核","target":"Kernel","line":3},{"title":"Linux","target":"Linux","line":3},{"title":"windows系统","target":"windows系统","line":3}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/内核漏洞.md#基于提权方面的考虑#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/内核漏洞.md#基于提权方面的考虑#{2}","lines":[20,20],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/内核漏洞.md#基于提权方面的考虑#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/内核漏洞.md#基于提权方面的考虑#{3}","lines":[21,22],"size":71,"outlinks":[{"title":"windows系统","target":"windows系统","line":1},{"title":"windows系统","target":"windows系统","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/内核漏洞.md#基于提权方面的考虑#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/内核漏洞.md#基于提权方面的考虑#{4}","lines":[23,24],"size":4,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/内核漏洞.md#内核漏洞的主要类型": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/内核漏洞.md#内核漏洞的主要类型","lines":[25,42],"size":544,"outlinks":[{"title":"内存","target":"内存","line":6},{"title":"缓冲区溢出","target":"缓冲区溢出","line":7},{"title":"内核","target":"Kernel","line":8},{"title":"内核","target":"Kernel","line":8},{"title":"内核","target":"Kernel","line":10},{"title":"内核","target":"Kernel","line":10},{"title":"内核","target":"Kernel","line":12}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/内核漏洞.md#内核漏洞的主要类型#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/内核漏洞.md#内核漏洞的主要类型#{1}","lines":[27,28],"size":84,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/内核漏洞.md#内核漏洞的主要类型#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/内核漏洞.md#内核漏洞的主要类型#{2}","lines":[29,30],"size":71,"outlinks":[{"title":"内存","target":"内存","line":2}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/内核漏洞.md#内核漏洞的主要类型#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/内核漏洞.md#内核漏洞的主要类型#{3}","lines":[31,32],"size":97,"outlinks":[{"title":"缓冲区溢出","target":"缓冲区溢出","line":1},{"title":"内核","target":"Kernel","line":2},{"title":"内核","target":"Kernel","line":2}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/内核漏洞.md#内核漏洞的主要类型#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/内核漏洞.md#内核漏洞的主要类型#{4}","lines":[33,34],"size":93,"outlinks":[{"title":"内核","target":"Kernel","line":2},{"title":"内核","target":"Kernel","line":2}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/内核漏洞.md#内核漏洞的主要类型#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/内核漏洞.md#内核漏洞的主要类型#{5}","lines":[35,37],"size":107,"outlinks":[{"title":"内核","target":"Kernel","line":2}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/内核漏洞.md#内核漏洞的主要类型#{6}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/内核漏洞.md#内核漏洞的主要类型#{6}","lines":[38,40],"size":69,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/内核漏洞.md#内核漏洞的主要类型#{7}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/内核漏洞.md#内核漏洞的主要类型#{7}","lines":[41,42],"size":4,"outlinks":[],"class_name":"SmartBlock"},
"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/内核漏洞.md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/内核漏洞.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05358155,-0.01312906,0.00156708,-0.04952252,0.00893169,-0.03931631,-0.04369886,0.05339142,0.06428578,0.01600038,-0.01785382,-0.04864735,0.03445239,0.02339755,0.06788499,0.00813204,-0.01414861,0.05155688,0.00680607,-0.01672927,0.0820921,-0.05272396,-0.00835009,-0.07651821,-0.0493518,0.02205146,0.05104792,-0.04321992,-0.02631167,-0.17128786,0.01100596,0.01686223,0.02475506,0.01938671,-0.01905306,-0.05892304,0.03662207,0.02709734,-0.01193882,0.04287376,0.01519255,0.0317115,-0.01610282,-0.013231,-0.02529181,-0.06968073,-0.02402555,-0.0479857,0.01939651,-0.04043742,-0.03176372,-0.01579155,-0.03311148,-0.03062403,-0.0738217,-0.00871412,0.06189306,0.00889221,0.08342336,0.0072847,0.01320381,0.02096551,-0.20666713,0.04603849,0.00710953,-0.0354446,-0.00116128,-0.06713896,0.0147512,0.04492844,-0.02590721,0.05553513,-0.01916211,0.05313798,0.05015668,-0.00558071,0.01803007,-0.01994981,-0.04431949,-0.0208421,-0.00517966,0.04511742,-0.03204511,-0.02065965,-0.01297143,0.01681272,-0.0267577,0.01991683,0.04244586,0.01339175,-0.01721239,-0.07501399,0.02444548,0.03854265,0.0063688,0.02550348,0.04712979,0.05144352,-0.12064873,0.1163615,-0.04097199,-0.01612177,-0.00584871,-0.0515734,0.0105049,-0.04226943,0.00032196,-0.0028881,-0.02650802,-0.02789971,-0.04914915,-0.02764443,0.06469857,-0.01128209,0.08408971,-0.00445257,0.02281048,-0.02907097,-0.01177514,-0.01403161,-0.01447301,0.02781706,0.0640824,-0.0261987,0.00692975,-0.03147582,0.00907829,0.07606094,0.0111802,0.02820915,0.05207605,-0.02853617,-0.05957386,-0.02854117,-0.00101441,0.01433331,-0.05371933,0.03698432,0.00228384,-0.06987387,-0.03695309,-0.02758328,0.03021433,-0.09022846,-0.05626172,0.11138183,-0.04969665,-0.01603439,0.01229987,-0.00273502,0.02310153,0.06421131,-0.01498333,-0.03054856,-0.01315821,-0.00957763,0.07992749,0.12300994,-0.04207082,-0.02249489,-0.03637811,0.00936702,-0.0804271,0.17225623,0.0306046,-0.03120972,-0.03028871,0.00341127,0.03149262,-0.02621971,0.00139036,-0.03720346,0.03064333,-0.01033582,0.05780094,-0.00335199,-0.05222708,-0.00829269,0.00258131,0.04151149,0.0912597,-0.00320107,-0.09696589,0.05031845,0.02570757,-0.10346621,-0.02306679,-0.05381366,-0.00494265,-0.04826039,-0.12845039,0.04576437,-0.02116624,-0.01723805,-0.05791348,-0.0621721,0.03177182,0.0216132,0.0430426,-0.04246716,0.06913848,0.05206418,-0.04987809,-0.00426294,-0.05308561,-0.03696131,-0.02056682,-0.02250074,0.0118702,0.048926,-0.0018747,0.00983406,-0.00852631,0.00054145,-0.03796707,0.01641528,0.00742581,0.02546614,0.04102467,0.05051238,0.02883923,-0.02578483,-0.0891353,-0.23842314,-0.05341277,-0.00179518,-0.03526008,-0.01104837,0.01690703,0.06373964,-0.00166581,0.08843647,0.03987243,0.06269509,0.03898746,-0.08213872,-0.0242338,0.02999165,-0.01222337,0.02023302,-0.02279404,-0.02157511,-0.01305494,0.008016,0.06421195,0.01702051,-0.0244647,0.02151805,-0.0369279,0.10282025,0.02755967,0.04294964,0.00402343,0.01613213,0.05678491,0.05902576,-0.10012782,0.0178872,0.05317872,-0.0339121,-0.05399514,-0.0256958,-0.00063151,-0.00179957,-0.00142407,-0.03479642,-0.07508181,0.0059065,-0.04341796,-0.00302364,0.00633372,0.01536079,0.07299445,-0.02058321,0.00004288,0.00921437,0.01450171,0.01961464,-0.02470414,-0.03834438,-0.03286775,-0.01173338,0.01777318,0.02139918,-0.0430848,0.03581772,0.00406499,0.02021032,-0.00000773,-0.01952489,-0.02316465,-0.03428125,-0.020919,-0.0463232,0.1601775,-0.00341816,-0.05301923,0.05882331,0.0408255,0.00232807,-0.0296459,0.02405517,0.00056593,0.06382171,0.0198861,0.04768824,0.02306333,0.00780909,0.02129327,0.01817328,0.00522342,0.05685252,-0.10324256,-0.0732756,-0.01155169,-0.03612365,0.0167531,0.12101689,-0.02221199,-0.28589723,0.01792958,0.01406411,-0.0174273,0.08544792,0.03340222,0.03532151,-0.03118969,-0.05226356,0.05150674,-0.06004403,0.06621376,-0.01625788,-0.01304014,-0.01489436,-0.02252879,0.08003294,-0.01337484,0.06865445,-0.01347863,-0.01299561,0.03557369,0.1906282,-0.01116831,0.03228542,-0.00197234,-0.0004852,0.0675877,0.068501,0.05211864,0.01875694,-0.07784592,0.02770222,-0.02591028,0.03011863,0.06733069,-0.04137456,0.00685235,0.06535675,0.03480742,-0.00409002,0.0566589,-0.06483378,0.0046652,0.07460179,-0.00135276,0.01133332,-0.05351216,0.01025859,0.016583,0.04548194,0.02874292,0.01112345,0.00258428,0.04579295,0.04944383,-0.01202577,-0.06531165,-0.06795874,-0.01008993,0.03631497,0.01856832,0.08021317,0.12568954,0.05117951],"last_embed":{"hash":"0064b64b4e22555cef410bfde8d1e00999f75f5d65af5e4228850af4f553ff39","tokens":469}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.05333147,0.0018451,-0.00248332,0.01198994,-0.02537936,0.02691359,0.06181386,-0.03156469,0.01206418,-0.01551154,-0.04315084,0.01647417,-0.05465923,-0.07330397,0.01833646,0.00507848,0.02411768,0.03266681,0.03400226,-0.00884039,-0.00831277,0.04624414,0.04173995,0.05044678,0.0276554,-0.01512118,0.00728194,-0.04118609,0.03822619,0.0596975,0.09595629,0.0190624,0.00972927,-0.03086336,-0.07517681,-0.00269778,0.04528467,-0.04730415,-0.02872906,0.06845364,-0.01715408,0.02428183,-0.02455204,0.03874985,-0.10125837,-0.00226738,0.01615766,-0.0315009,-0.0048236,0.0206517,0.04907412,-0.09155727,0.02681989,0.03592482,-0.02261918,0.04560085,-0.05892429,-0.03355845,-0.00555189,-0.00313129,0.02539265,-0.00566111,0.07874683,0.03259515,0.02156402,0.04876103,-0.02094185,-0.04714283,0.02041209,-0.00390263,-0.05228562,0.02559326,0.00317433,-0.00318137,0.08479617,0.02245472,0.01928797,0.00779645,-0.00439055,-0.01452368,-0.0890471,-0.01173974,-0.01153873,0.07734451,0.05771377,-0.06351236,0.0424228,-0.02007562,-0.00003453,-0.02614598,-0.01276194,0.03161467,-0.0253702,-0.00714967,0.04023343,-0.06287785,0.01182628,0.00403306,-0.01293313,-0.02802814,-0.0301969,-0.02336662,-0.08198787,0.01535975,-0.03280049,-0.06055079,-0.05404438,0.05255889,-0.02901307,-0.02499366,0.02870555,0.0259639,-0.02231818,-0.00700388,-0.07049978,0.02772495,-0.03066375,-0.01352617,0.03098327,0.04706482,0.03359612,0.03818467,0.02736834,-0.04546658,-0.03236134,-0.02218967,-0.0128881,-0.02777902,0.05753998,-0.04597951,0.01687757,0.04340763,-0.05805218,-0.03612155,-0.02135622,0.03052905,0.04529762,-0.04750469,0.02988987,-0.02739136,0.01360927,-0.01929453,0.02129761,0.01897581,-0.05737205,0.0910839,-0.0418115,-0.02880305,-0.02886593,0.03763034,0.01568377,0.03943458,0.02478681,-0.05352493,0.01819518,-0.01616157,0.02589672,0.04181267,-0.00388917,-0.03319847,-0.01265999,0.01836059,0.02854737,0.01969635,0.02156096,-0.01463181,-0.03193012,0.0186465,0.00142586,0.02273412,-0.00637107,0.0200123,0.01807689,0.01024212,0.00329844,-0.04835032,0.01648805,0.0324863,-0.0175882,-0.0153377,0.02262558,-0.03869721,0.00047681,0.00259427,-0.00567048,0.05744601,-0.02748269,-0.01866112,0.05059767,0.03072117,0.03208617,0.02818546,-0.00278859,0.00591742,0.03090312,0.02691342,-0.03498084,-0.06260771,-0.02836096,0.02962423,0.0053079,-0.05151628,0.00868404,-0.07620119,0.06432216,-0.00275858,0.01296015,-0.06965821,-0.07948548,0.05387697,0.01987401,0.03280605,-0.00134598,0.01764691,-0.02950354,0.07988696,-0.03195756,0.03264022,0.04399384,-0.01503876,0.00483026,-0.02029087,-0.05267043,-0.0033538,-0.02745526,-0.03743507,0.02904322,0.0173481,0.01441615,-0.02996203,0.03831838,0.0477857,-0.03612622,0.00166306,-0.01845177,0.01498519,0.04723699,0.04761861,0.0247998,0.0060005,0.01530279,0.02591577,0.01091452,-0.01712857,-0.00107046,-0.04107106,-0.05858451,0.03800583,0.01525463,0.07209231,0.01936129,-0.04300072,-0.01124968,0.05970762,0.0303125,-0.03520101,-0.01403806,-0.02128436,0.0092171,-0.02354414,0.02005908,-0.00495462,-0.05758696,0.03015258,-0.01689168,-0.01957915,-0.03257001,0.02802654,-0.03955865,0.05209229,0.01850483,-0.02459934,0.06509848,0.00889577,-0.02860718,0.04572447,-0.02930032,0.01830087,-0.04186083,-0.0188087,-0.03932267,-0.02943519,-0.00325227,-0.01421194,-0.0535677,0.04437793,0.02034712,-0.01420281,0.02332432,0.00312457,0.04845908,0.00807812,-0.04560422,0.04504058,-0.08211749,-0.01725492,-0.01536471,-0.00029687,-0.03558782,-0.0678435,0.00874522,0.05718883,0.02843109,-0.00843778,-0.00236035,0.02872547,0.00329748,0.04622891,-0.01492961,-0.05413252,0.00487074,-0.05306622,0.01602327,-0.03318179,0.0235105,0.0931723,-0.00350372,0.01547996,-0.03897337,0.01242994,-0.07435647,0.027002,0.01496806,-0.02441656,0.01453073,-0.06904552,0.02642801,0.01826808,0.06399815,0.06382502,-0.01113345,0.00446543,0.01139754,-0.03089411,0.0295139,-0.00954349,-0.01523331,-0.04824262,0.02969405,-0.02087959,0.02216054,-0.04331579,-0.00190802,-0.03333127,0.01016661,0.06906681,-0.00574466,-0.00230533,-0.00669917,-0.02766736,0.02741657,-0.02541783,-0.00389838,-0.04452434,0.04157141,-0.01748176,-0.02550986,-0.02800817,-0.02020142,0.02275813,0.00073011,-0.03822097,-0.02464333,0.00083074,-0.05073337,0.03535783,0.03760049,0.04616416,-0.05047397,-0.07731213,0.03141756,0.00630879,-0.02605472,0.02690443,0.03803739,-0.01190009,-0.00925526,0.02211347,0.00027681,-0.04659389,-0.01949711,-0.05603274,-0.0103342,-0.0488103,-0.08044843,-0.05078195,-0.0087804,-0.03777409,-0.06512028,-0.04285901,-0.00837712,0.0302963,-0.04372116,0.01628857,0.00549078,0.02731753,0.04387075,-0.01104802,-0.01772155,-0.00977745,0.00692372,-0.02164836,0.03604868,0.01506941,0.00026673,0.06075535,-0.04257508,0.03449408,-0.04303076,-0.05337978,0.03087902,0.07027613,0.08152715,0.007551,-0.00435851,0.01082848,-0.00572109,-0.0595838,-0.00352582,0.06333196,0.0236926,-0.04621019,0.05267553,0.065741,0.02022762,0.01688771,-0.01763969,-0.02990569,0.00419162,0.0335909,-0.01347811,-0.03213925,-0.06261237,-0.02520429,-0.02648157,-0.01246015,0.06250867,-0.06063877,-0.01110357,0.07017461,-0.04923752,-0.00622294,0.03234601,-0.00115318,0.05547078,0.05164777,-0.02661395,-0.08814336,-0.00379725,-0.01083008,-0.0025545,0.02343026,-0.0772819,0.01600338,0.02861827,0.00861317,0.03103442,0.01505651,-0.01403094,-0.00415585,-0.02493643,-0.02305677,0.01018843,-0.02622472,0.0326774,0.01889149,0.04196811,0.01042262,-0.08038755,-0.00162523,-0.01131257,-0.01940829,-0.01426691,0.00667393,0.01829913,0.02822253,-0.0220632,0.01983538,-0.02148849,0.0252861,0.0326806,0.05327519,0.02681132,0.00694368,0.02836758,0.03663722,-0.04600719,0.04674131,0.05066982,-0.01048492,-0.01870051,0.03937332,0.00655646,0.04646242,0.00624215,-0.02715423,-0.02716072,0.05108683,0.01026107,0.01944566,-0.0250405,0.00544841,-0.01115619,-0.00676687,0.01213661,0.03045116,-0.02370268,-0.00756889,0.03643499,-0.01892626,-0.03593306,-0.00597461,0.08641185,0.06973408,-0.04658128,-0.06373017,-0.01733204,0.02467597,-0.00353293,0.01178216,-0.04046575,0.04070727,-0.06480456,0.00106543,0.0105133,-0.0538777,-0.0568415,-0.00185678,-0.00699863,-0.02489981,-0.06054397,-0.00331502,0.01680206,-0.00592898,0.03604392,-0.06460457,0.02991887,-0.05038331,-0.01437602,0.01243929,0.05353344,-0.03117455,-0.0023822,0.01708298,-0.03718093,-0.02370596,0.00142908,-0.03080332,0.07136675,-0.02812778,-0.00744878,0.00390622,0.02192166,0.00318795,-0.04575229,0.00148302,0.02260933,-0.05903727,-0.01853367,0.0606381,0.0195787,0.06993464,-0.06688304,0.03092643,-0.05581787,0.01634558,-0.04064677,0.01737352,0.00173699,0.03720163,-0.04749762,-0.02172976,0.05978216,-0.00792209,-0.03753277,0.01123036,0.0128878,-0.03174822,0.03186087,-0.02184123,0.00066939,0.01986306,0.02946448,-0.0010162,-0.00600238,0.00420386,0.0083614,0.0272919,0.01021323,0.01066105,0.00588432,0.00188581,-0.00442541,-0.03758782,-0.03157254,-0.01109463,-0.06404173,0.02003298,0.02864731,-0.03458195,0.00721358,0.02526936,-0.02000297,-0.02031828,0.01164103,-0.11494768,-0.04210066,-0.04686304,0.04262919,0.05059562,0.01100921,0.00985174,0.03566134,-0.02279787,0.0285936,-0.0266317,-0.00284693,-0.00176107,-0.01764244,0.0345173,-0.02331397,-0.07386454,-0.04302959,-0.00714793,-0.0123825,0.07293166,-0.01535174,0.01009841,0.06203523,0.00658072,-0.05552763,-0.04811829,0.00429624,0.05931644,-0.00888672,0.00038936,-0.05757725,0.00245932,0.00384555,-0.01816975,0.06702597,0.03192703,0.02155618,0.01693074,0.00861805,-0.06027753,-0.00787136,0.01539701,0.0393495,-0.03593403,0.00395824,-0.06094343,-0.00792905,0.00374128,-0.0157928,-0.00234496,0.00267588,0.00146822,0.01401502,0.08539803,-0.03062128,-0.03133949,-0.03552245,-0.02145004,-0.02550205,-0.00594406,0.07823745,-0.0016273,-0.05074373,0.03275183,-0.02725284,-0.01639772,-0.01790428,-0.00754477,-0.0316436,0.01944625,0.00887688,0.05149681,-0.02469523,-0.05039069,-0.02796737,-0.00004676,-0.00960048,-0.00994998,0.02644003,-0.03803654,-0.00772361,-0.02462978,-0.01158604,0.02052532,-0.06599044,0.03496331,0.01709654,-0.00176007,-0.02964099,-0.06621187,0.05272646,-0.05046444,0.01947185,0.00901941,-0.04288813,-0.03337726,-0.01520239,0.00627276,-0.05691971,-0.01058099,0.00949192,-0.01634897,0.046117,0.02505206,-0.05470738,0.04152668,-0.01009574,0.03741151,0.02022184,-0.03719804,-0.0007202,0.0997111,-0.09242322,0.01157021,-0.03409554,0.00571592,0.02742625,0.05142516,-0.06194679,-0.0463429,-0.02396964,-0.03284683,0.0241393,0.00479164,0.00375225,-0.0933897,0.04226168,0.00722213,0.03710624,0.00057601,0.00946709,-0.0027916,0.04542392,-0.00528394,0.0082302,0.01031614,-0.01665644,-0.0224717,-0.04018409,0.03723766,0.04984463,0.00695841,0.0432597,0.0361503,-0.02039021,0.03923188,-0.01207139,0.10243387,0.01316407,0.02267308,-0.01886138,-0.06064653,8.4e-7,0.01324562,-0.01012593,0.02609115,-0.01638514,-0.01054924,0.00339674,-0.02919086,-0.02477361,0.05085814],"last_embed":{"tokens":478,"hash":"164se62"}}},"last_read":{"hash":"164se62","at":1751597376083},"class_name":"SmartSource","outlinks":[{"title":"Kernel","target":"Kernel","line":6},{"title":"内核","target":"Kernel","line":17},{"title":"内核","target":"Kernel","line":19},{"title":"Linux","target":"Linux","line":19},{"title":"windows系统","target":"windows系统","line":19},{"title":"windows系统","target":"windows系统","line":21},{"title":"windows系统","target":"windows系统","line":21},{"title":"内存","target":"内存","line":30},{"title":"缓冲区溢出","target":"缓冲区溢出","line":31},{"title":"内核","target":"Kernel","line":32},{"title":"内核","target":"Kernel","line":32},{"title":"内核","target":"Kernel","line":34},{"title":"内核","target":"Kernel","line":34},{"title":"内核","target":"Kernel","line":36}],"metadata":{"aliases":null,"tags":["网络安全/漏洞"],"基础知识":["[[Kernel]]"],"cssclasses":["editor-full"]},"blocks":{"#---frontmatter---":[1,9],"#简介":[10,15],"#简介#{1}":[11,15],"#基于提权方面的考虑":[16,24],"#基于提权方面的考虑#{1}":[17,19],"#基于提权方面的考虑#{2}":[20,20],"#基于提权方面的考虑#{3}":[21,22],"#基于提权方面的考虑#{4}":[23,24],"#内核漏洞的主要类型":[25,42],"#内核漏洞的主要类型#{1}":[27,28],"#内核漏洞的主要类型#{2}":[29,30],"#内核漏洞的主要类型#{3}":[31,32],"#内核漏洞的主要类型#{4}":[33,34],"#内核漏洞的主要类型#{5}":[35,37],"#内核漏洞的主要类型#{6}":[38,40],"#内核漏洞的主要类型#{7}":[41,42]},"last_import":{"mtime":1747536242436,"size":1973,"at":1749024987637,"hash":"164se62"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/内核漏洞.md","last_embed":{"hash":"164se62","at":1751597376083}},